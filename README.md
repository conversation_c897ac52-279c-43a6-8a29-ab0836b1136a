# Vue Table Component

A high-performance Vue 3 table component for B2B systems with TypeScript support.

## Features

- 🚀 Vue 3 + TypeScript
- 🎨 Tailwind CSS 4 with theme system
- 📱 Responsive design
- ⚡ Virtual scrolling for large datasets
- 🎯 Full keyboard navigation
- 🌙 Dark mode support
- 📊 Sorting, filtering, and pagination
- ✏️ Inline editing
- 📤 Data export (CSV, Excel)
- ♿ Accessibility compliant

## Development Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Build library
npm run build:lib

# Run tests
npm run test

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Format code
npm run format
```

## Project Structure

```
src/
├── components/          # Vue components
│   └── Table/          # Table component
├── composables/        # Vue composables
├── types/             # TypeScript type definitions
├── utils/             # Utility functions
└── styles/            # CSS and theme files
    └── themes/        # Theme configurations
```

## License

MIT
