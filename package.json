{"name": "@happy-table/vue3", "version": "0.1.1", "description": "A high-performance Vue 3 table component for B2B systems with TypeScript support", "type": "module", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}, "./style.css": "./dist/vue3.css"}, "files": ["dist"], "scripts": {"dev": "vite --port 3200", "build": "vite build", "build:lib": "vite build --mode lib", "build:analyze": "vite build --mode lib -- --analyze", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --noEmit", "ci": "npm run lint:check && npm run type-check && npm run test && npm run build:lib", "prepublishOnly": "npm run ci"}, "dependencies": {"@iconify/icons-lucide": "^1.2.135", "@iconify/icons-mdi": "^1.2.48", "@iconify/vue": "^5.0.0", "@tailwindcss/vite": "^4.1.12", "@vitest/coverage-v8": "^3.2.4", "vue": "^3.4.0"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^6.0.1", "@vitest/ui": "^3.2.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "jsdom": "^23.0.0", "prettier": "^3.1.0", "tailwindcss": "^4.1.12", "typescript": "^5.3.0", "vite": "^7.1.3", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4", "vue-tsc": "^3.0.6"}, "keywords": ["vue", "vue3", "table", "component", "typescript", "b2b", "data-table", "virtual-scroll", "tailwind"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/kaizhoumasha/happyGrid.git"}, "bugs": {"url": "https://github.com/kaizhoumasha/happyGrid/issues"}, "homepage": "https://github.com/kaizhoumasha/happyGrid#readme"}