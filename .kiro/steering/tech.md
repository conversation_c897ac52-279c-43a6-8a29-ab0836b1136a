# Technology Stack & Build System

## Core Technologies

- **Vue 3.4+**: Composition API, `<script setup>`, reactive system
- **TypeScript 5.3+**: Strict mode enabled, comprehensive type definitions
- **Tailwind CSS 4**: CSS variables, custom theme system, responsive design
- **Vite 5**: Build tool with HMR, library mode support
- **Vitest**: Testing framework with coverage reporting

## Development Dependencies

- **ESLint + Prettier**: Code formatting and linting
- **Vue Test Utils**: Component testing utilities
- **JSDOM**: DOM environment for testing
- **vite-plugin-dts**: TypeScript declaration generation

## 🚨 开发服务器使用规则

**绝对禁止启动新的开发服务器！**

- 开发服务器已在端口 3200 运行，支持完整热更新
- 所有文件修改会自动反映到浏览器，无需任何手动操作
- 如需测试功能变更，直接查看 http://localhost:3200
- 切勿使用 `npm run dev` 或任何启动开发服务器的命令

## Build System

### Development Commands

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run build:lib    # Build as library
npm run preview      # Preview production build
```

### Testing Commands

```bash
npm run test         # Run tests
npm run test:ui      # Run tests with UI
npm run test:coverage # Run tests with coverage report
```

### Code Quality Commands

```bash
npm run lint         # Lint and fix code
npm run lint:check   # Check linting without fixing
npm run format       # Format code with Prettier
npm run format:check # Check formatting
npm run type-check   # TypeScript type checking
```

## Project Configuration

### Path Aliases

- `@/*` → `src/*`
- `@/components/*` → `src/components/*`
- `@/composables/*` → `src/composables/*`
- `@/types/*` → `src/types/*`
- `@/utils/*` → `src/utils/*`
- `@/styles/*` → `src/styles/*`

### Build Modes

- **Development**: HMR, source maps, dev server
- **Production**: Optimized bundle, tree-shaking
- **Library**: ES/CJS modules, TypeScript declarations, external Vue

### Test Configuration

- **Coverage Threshold**: 90% for branches, functions, lines, statements
- **Environment**: JSDOM for DOM testing
- **Globals**: Vitest globals enabled for test files
