# Vue Table Component

A high-performance Vue 3 table component library designed for B2B systems with comprehensive TypeScript support.

## Core Features

- **Vue 3 + Composition API**: Modern reactive architecture
- **TypeScript First**: Complete type safety and IntelliSense support
- **Tailwind CSS 4**: Advanced theming system with CSS variables
- **Performance Optimized**: Virtual scrolling for large datasets
- **Accessibility Compliant**: WCAG AA standards with keyboard navigation
- **Enterprise Ready**: Sorting, filtering, pagination, inline editing, data export

## Target Use Cases

- B2B dashboards and admin panels
- Data-heavy applications requiring high performance
- Enterprise systems needing accessibility compliance
- Applications requiring extensive customization and theming

## Key Differentiators

- Comprehensive theme system with smooth transitions
- Full keyboard navigation support
- Virtual scrolling for handling large datasets
- Modular architecture with composable utilities
- Extensive test coverage (90%+ threshold)

## 🚨 开发服务器使用规则

**绝对禁止启动新的开发服务器！**

- 开发服务器已在端口 3200 运行，支持完整热更新
- 所有文件修改会自动反映到浏览器，无需任何手动操作
- 如需测试功能变更，直接查看 http://localhost:3200
- 切勿使用 `npm run dev` 或任何启动开发服务器的命令
