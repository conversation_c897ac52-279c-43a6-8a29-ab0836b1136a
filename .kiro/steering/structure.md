# Project Structure & Organization

## Directory Layout

```
src/
├── components/          # Vue components (main library exports)
│   ├── Table/          # Core table component and subcomponents
│   ├── ThemeProvider/  # Theme system components
│   ├── ThemeSwitcher/  # Theme switching UI
│   └── index.ts        # Component exports
├── composables/        # Vue composables (reusable logic)
├── types/             # TypeScript type definitions
├── utils/             # Pure utility functions
├── styles/            # CSS and theme configurations
│   └── themes/        # Theme presets and system
├── playground/        # Development demos and examples
└── integration/       # Integration tests
```

## Architecture Patterns

### Component Organization
- **Atomic Design**: Components organized by complexity and reusability
- **Co-location**: Tests alongside components (`*.test.ts`)
- **Index Exports**: Each folder has `index.ts` for clean imports
- **Single Responsibility**: Each component has a focused purpose

### Composables Pattern
- **Pure Logic**: Business logic separated from UI components
- **Reactive State**: Use Vue's reactivity system consistently
- **Testable**: Each composable has comprehensive test coverage
- **Reusable**: Designed for use across multiple components

### Type System
- **Strict TypeScript**: All code must be fully typed
- **Interface Segregation**: Small, focused interfaces
- **Generic Types**: Flexible, reusable type definitions
- **Export Strategy**: Re-export all types from `types/index.ts`

## File Naming Conventions

### Components
- **PascalCase**: `TableHeader.vue`, `ThemeProvider.vue`
- **Descriptive**: Names clearly indicate component purpose
- **Consistent**: Follow Vue 3 style guide conventions

### Composables
- **camelCase**: `useTheme.ts`, `usePagination.ts`
- **Prefix**: All composables start with `use`
- **Descriptive**: Name indicates the functionality provided

### Types
- **camelCase**: `table.ts`, `theme.ts`, `events.ts`
- **Grouped**: Related types in same file
- **Exported**: All types available from main index

### Tests
- **Co-located**: `Component.test.ts` next to `Component.vue`
- **Descriptive**: Test names describe behavior being tested
- **Comprehensive**: Cover all public APIs and edge cases

## Import/Export Strategy

### Internal Imports
```typescript
// Use path aliases for internal imports
import type { TableConfig } from '@/types'
import { useTheme } from '@/composables'
import TableHeader from '@/components/Table/TableHeader.vue'
```

### Library Exports
```typescript
// Main entry point exports everything
export { default as VueTable } from './components/Table/Table.vue'
export * from './components'
export * from './composables'
export type * from './types'
```

### Component Exports
```typescript
// Each component folder has index.ts
export { default as ThemeProvider } from './ThemeProvider.vue'
export * from './types'
```

## Code Organization Principles

### Separation of Concerns
- **Components**: UI rendering and user interaction
- **Composables**: Business logic and state management
- **Utils**: Pure functions and data transformations
- **Types**: Type definitions and interfaces

### Dependency Direction
- **Components** depend on **Composables** and **Types**
- **Composables** depend on **Utils** and **Types**
- **Utils** depend only on **Types**
- **Types** have no dependencies

### Testing Strategy
- **Unit Tests**: All composables and utilities
- **Component Tests**: All Vue components
- **Integration Tests**: Cross-component functionality
- **Coverage**: Maintain 90%+ test coverage threshold