<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue Table Component 演示效果</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #1f2937;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .table-demo {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .table-header {
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
        }
        
        .header-cell {
            padding: 12px 16px;
            font-weight: 600;
            color: #374151;
            border-right: 1px solid #e5e7eb;
            flex: 1;
            text-align: left;
        }
        
        .header-cell:last-child {
            border-right: none;
        }
        
        .header-cell.center {
            text-align: center;
        }
        
        .header-cell.right {
            text-align: right;
        }
        
        .table-body {
            background: white;
        }
        
        .table-row {
            display: flex;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.15s;
        }
        
        .table-row:hover {
            background: #f9fafb;
        }
        
        .table-row:nth-child(even) {
            background: #f8fafc;
        }
        
        .table-row:nth-child(even):hover {
            background: #f1f5f9;
        }
        
        .table-cell {
            padding: 12px 16px;
            color: #374151;
            border-right: 1px solid #f3f4f6;
            flex: 1;
        }
        
        .table-cell:last-child {
            border-right: none;
        }
        
        .table-cell.center {
            text-align: center;
        }
        
        .table-cell.right {
            text-align: right;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .feature-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .feature-card h3 {
            color: #059669;
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
        }
        
        .feature-card ul {
            margin: 0;
            padding-left: 1.2rem;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .feature-card li {
            margin-bottom: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vue Table Component 演示效果</h1>
        
        <!-- 基础表格演示 -->
        <div class="table-demo">
            <div class="table-header">
                <div class="header-cell">姓名</div>
                <div class="header-cell center">年龄</div>
                <div class="header-cell">邮箱</div>
                <div class="header-cell center">状态</div>
            </div>
            <div class="table-body">
                <div class="table-row">
                    <div class="table-cell">张三</div>
                    <div class="table-cell center">30</div>
                    <div class="table-cell"><EMAIL></div>
                    <div class="table-cell center">
                        <span class="status-badge status-active">在职</span>
                    </div>
                </div>
                <div class="table-row">
                    <div class="table-cell">李四</div>
                    <div class="table-cell center">25</div>
                    <div class="table-cell"><EMAIL></div>
                    <div class="table-cell center">
                        <span class="status-badge status-active">在职</span>
                    </div>
                </div>
                <div class="table-row">
                    <div class="table-cell">王五</div>
                    <div class="table-cell center">35</div>
                    <div class="table-cell"><EMAIL></div>
                    <div class="table-cell center">
                        <span class="status-badge status-inactive">离职</span>
                    </div>
                </div>
                <div class="table-row">
                    <div class="table-cell">赵六</div>
                    <div class="table-cell center">28</div>
                    <div class="table-cell"><EMAIL></div>
                    <div class="table-cell center">
                        <span class="status-badge status-active">在职</span>
                    </div>
                </div>
                <div class="table-row">
                    <div class="table-cell">钱七</div>
                    <div class="table-cell center">32</div>
                    <div class="table-cell"><EMAIL></div>
                    <div class="table-cell center">
                        <span class="status-badge status-pending">休假</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 功能特性展示 -->
        <div class="features">
            <div class="feature-card">
                <h3>✅ 基础渲染</h3>
                <ul>
                    <li>表格结构渲染</li>
                    <li>数据绑定</li>
                    <li>列配置</li>
                    <li>样式主题</li>
                </ul>
            </div>
            <div class="feature-card">
                <h3>✅ 表头功能</h3>
                <ul>
                    <li>列标题显示</li>
                    <li>列宽度计算</li>
                    <li>排序指示器</li>
                    <li>列对齐方式</li>
                </ul>
            </div>
            <div class="feature-card">
                <h3>✅ 表体功能</h3>
                <ul>
                    <li>数据行渲染</li>
                    <li>空状态显示</li>
                    <li>加载状态</li>
                    <li>行交互状态</li>
                </ul>
            </div>
            <div class="feature-card">
                <h3>✅ 单元格功能</h3>
                <ul>
                    <li>数据显示</li>
                    <li>自定义渲染</li>
                    <li>对齐配置</li>
                    <li>交互状态</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>