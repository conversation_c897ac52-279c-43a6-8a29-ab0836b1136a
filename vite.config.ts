import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'
import tailwindcss from '@tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'

  return {
    plugins: [
      vue(),
      tailwindcss(),
      ...(isLib
        ? [
          dts({
            include: ['src/**/*'],
            exclude: ['src/**/*.test.ts', 'src/**/*.spec.ts'],
            outDir: 'dist',
            copyDtsFiles: true
          })
        ]
        : [])
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@/components': resolve(__dirname, 'src/components'),
        '@/composables': resolve(__dirname, 'src/composables'),
        '@/types': resolve(__dirname, 'src/types'),
        '@/utils': resolve(__dirname, 'src/utils'),
        '@/styles': resolve(__dirname, 'src/styles')
      }
    },
    build: isLib
      ? {
        lib: {
          entry: {
            index: resolve(__dirname, 'src/index.ts'),
            full: resolve(__dirname, 'src/full.ts'),
            composables: resolve(__dirname, 'src/composables.ts'),
            utils: resolve(__dirname, 'src/utils.ts'),
            themes: resolve(__dirname, 'src/themes.ts')
          },
          formats: ['es', 'cjs']
        },
        rollupOptions: {
          external: ['vue'],
          output: {
            globals: {
              vue: 'Vue'
            },
            // 优化打包输出
            manualChunks: undefined,
            // 启用更好的 Tree-shaking
            preserveModules: false,
            // 优化导出格式
            exports: 'named',
            // 更好的文件名
            entryFileNames: '[name].[format].js',
            chunkFileNames: '[name]-[hash].[format].js'
          },
          // 优化外部依赖处理
          treeshake: {
            moduleSideEffects: false,
            propertyReadSideEffects: false,
            tryCatchDeoptimization: false,
            // 更激进的tree-shaking
            unknownGlobalSideEffects: false
          }
        },
        cssCodeSplit: false,
        // 启用压缩和优化
        minify: 'esbuild',
        target: 'es2020',
        // 移除调试代码
        define: {
          __DEV__: false
        }
      }
      : undefined,
    test: {
      globals: true,
      environment: 'jsdom',
      coverage: {
        provider: 'v8',
        reporter: ['text', 'html', 'lcov'],
        threshold: {
          global: {
            branches: 90,
            functions: 90,
            lines: 90,
            statements: 90
          }
        }
      }
    }
  }
})
