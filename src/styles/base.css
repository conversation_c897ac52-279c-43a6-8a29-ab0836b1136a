/* Base styles and CSS variables for Vue Table Component */
@import 'tailwindcss';

/* Global reset for full screen layout */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Tailwind CSS 4 Theme Configuration */
@theme {
  /* Custom Colors - 自定义颜色 */
  --color-table-primary: #3b82f6;
  --color-table-secondary: #64748b;
  --color-table-accent: #06b6d4;
  --color-table-success: #10b981;
  --color-table-warning: #f59e0b;
  --color-table-error: #ef4444;
  --color-table-info: #06b6d4;

  /* Surface Colors - 表面颜色 */
  --color-table-bg: #ffffff;
  --color-table-bg-secondary: #f8fafc;
  --color-table-surface: #f8fafc;
  --color-table-hover: #f1f5f9;
  --color-table-selected: #dbeafe;
  --color-table-header-bg: #f8fafc;
  --color-table-row-hover-bg: #f1f5f9;
  --color-table-row-selected-bg: #dbeafe;

  /* Border Colors - 边框颜色 */
  --color-table-border: #e2e8f0;
  --color-table-border-hover: #cbd5e1;

  /* Text Colors - 文本颜色 */
  --color-table-text: #1e293b;
  --color-table-text-secondary: #64748b;
  --color-table-text-muted: #94a3b8;

  /* Custom Breakpoints - 自定义断点 */
  --breakpoint-3xl: 1920px;
  --breakpoint-4xl: 2560px;

  /* Custom Font Families - 自定义字体 */
  --font-family-table: system-ui, -apple-system, sans-serif;
  --font-family-mono:
    'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Custom Spacing - 自定义间距 */
  --spacing-table-xs: 0.25rem;
  --spacing-table-sm: 0.5rem;
  --spacing-table-md: 1rem;
  --spacing-table-lg: 1.5rem;
  --spacing-table-xl: 2rem;
  --spacing-table-2xl: 3rem;

  /* Custom Border Radius - 自定义圆角 */
  --radius-table: 0.375rem;
  --radius-table-sm: 0.25rem;
  --radius-table-lg: 0.5rem;
  --radius-table-xl: 0.75rem;

  /* Border Radius Utilities */
  --border-radius-table: 0.375rem;

  /* Custom Shadows - 自定义阴影 */
  --shadow-table: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-table-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-table-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-table-focus: 0 0 0 3px rgb(59 130 246 / 0.1);

  /* Animation Durations - 动画持续时间 */
  --animate-table-fast: 150ms;
  --animate-table-normal: 300ms;
  --animate-table-slow: 500ms;
}

/* Custom Utility Classes using @utility directive */
@utility bg-table-bg {
  background-color: var(--color-table-bg);
}

@utility bg-table-bg-secondary {
  background-color: var(--color-table-bg-secondary);
}

@utility bg-table-header-bg {
  background-color: var(--color-table-header-bg);
}

@utility bg-table-row-hover-bg {
  background-color: var(--color-table-row-hover-bg);
}

@utility bg-table-row-selected-bg {
  background-color: var(--color-table-row-selected-bg);
}

@utility bg-table-primary {
  background-color: var(--color-table-primary);
}

@utility bg-table-secondary {
  background-color: var(--color-table-secondary);
}

@utility bg-table-accent {
  background-color: var(--color-table-accent);
}

@utility bg-table-success {
  background-color: var(--color-table-success);
}

@utility bg-table-warning {
  background-color: var(--color-table-warning);
}

@utility bg-table-error {
  background-color: var(--color-table-error);
}

@utility bg-table-info {
  background-color: var(--color-table-info);
}

@utility text-table-text {
  color: var(--color-table-text);
}

@utility text-table-text-secondary {
  color: var(--color-table-text-secondary);
}

@utility text-table-text-muted {
  color: var(--color-table-text-muted);
}

@utility text-table-primary {
  color: var(--color-table-primary);
}

@utility border-table-border {
  border-color: var(--color-table-border);
}

@utility border-table-border-hover {
  border-color: var(--color-table-border-hover);
}

@utility border-table-primary {
  border-color: var(--color-table-primary);
}

@utility rounded-table {
  border-radius: var(--border-radius-table);
}

@utility shadow-table-sm {
  box-shadow: var(--shadow-table-sm);
}

@utility shadow-table {
  box-shadow: var(--shadow-table);
}

@utility shadow-table-lg {
  box-shadow: var(--shadow-table-lg);
}

@utility shadow-table-focus {
  box-shadow: var(--shadow-table-focus);
}

@utility duration-table-fast {
  transition-duration: var(--animate-table-fast);
}

@utility duration-table-normal {
  transition-duration: var(--animate-table-normal);
}

@utility duration-table-slow {
  transition-duration: var(--animate-table-slow);
}

@utility font-table {
  font-family: var(--font-family-table);
}

@utility space-x-table-* {
  & > * + * {
    margin-left: var(--spacing-table-*);
  }
}

/* CSS Variables for Theme System */
:root {
  /* 默认主题 - Default Theme */
  --table-primary: #3b82f6;
  --table-secondary: #64748b;
  --table-accent: #06b6d4;
  --table-background: #ffffff;
  --table-surface: #f8fafc;
  --table-border: #e2e8f0;
  --table-text: #1e293b;
  --table-text-secondary: #64748b;
  --table-hover: #f1f5f9;
  --table-selected: #dbeafe;
  --table-focus: #3b82f6;
  --table-error: #ef4444;
  --table-warning: #f59e0b;
  --table-success: #10b981;
  --table-info: #06b6d4;

  /* Spacing */
  --table-spacing-xs: 0.25rem;
  --table-spacing-sm: 0.5rem;
  --table-spacing-md: 1rem;
  --table-spacing-lg: 1.5rem;
  --table-spacing-xl: 2rem;

  /* Border Radius */
  --table-border-radius: 0.375rem;
  --table-border-radius-sm: 0.25rem;
  --table-border-radius-lg: 0.5rem;

  /* Typography */
  --table-font-size-xs: 0.75rem;
  --table-font-size-sm: 0.875rem;
  --table-font-size-base: 1rem;
  --table-font-size-lg: 1.125rem;
  --table-font-size-xl: 1.25rem;

  --table-font-weight-normal: 400;
  --table-font-weight-medium: 500;
  --table-font-weight-semibold: 600;
  --table-font-weight-bold: 700;

  /* Shadows */
  --table-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --table-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --table-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --table-shadow-focus: 0 0 0 3px rgb(59 130 246 / 0.1);

  /* Fixed column shadows for horizontal scrolling */
  --table-shadow-fixed-left: 2px 0 4px rgba(0, 0, 0, 0.1);
  --table-shadow-fixed-right: -2px 0 4px rgba(0, 0, 0, 0.1);
  --table-shadow-fixed-header-left: 2px 0 4px rgba(0, 0, 0, 0.15);
  --table-shadow-fixed-header-right: -2px 0 4px rgba(0, 0, 0, 0.15);

  /* Transitions */
  --table-transition-duration: 150ms;
}

/* 深色主题 - Dark Theme */
[data-theme='dark'] {
  --table-primary: #60a5fa;
  --table-secondary: #94a3b8;
  --table-accent: #22d3ee;
  --table-background: #0f172a;
  --table-surface: #1e293b;
  --table-border: #334155;
  --table-text: #f1f5f9;
  --table-text-secondary: #94a3b8;
  --table-hover: #334155;
  --table-selected: #1e40af;
  --table-focus: #60a5fa;
  --table-error: #f87171;
  --table-warning: #fbbf24;
  --table-success: #34d399;
  --table-info: #22d3ee;

  /* Dark theme fixed column shadows */
  --table-shadow-fixed-left: 2px 0 6px rgba(0, 0, 0, 0.3);
  --table-shadow-fixed-right: -2px 0 6px rgba(0, 0, 0, 0.3);
  --table-shadow-fixed-header-left: 2px 0 6px rgba(0, 0, 0, 0.4);
  --table-shadow-fixed-header-right: -2px 0 6px rgba(0, 0, 0, 0.4);
}

/* 企业蓝主题 - Enterprise Blue Theme */
[data-theme='enterprise'] {
  --table-primary: #1e40af;
  --table-secondary: #475569;
  --table-accent: #0ea5e9;
  --table-background: #f8fafc;
  --table-surface: #ffffff;
  --table-border: #cbd5e1;
  --table-text: #0f172a;
  --table-text-secondary: #475569;
  --table-hover: #e2e8f0;
  --table-selected: #dbeafe;
  --table-focus: #1e40af;
  --table-error: #dc2626;
  --table-warning: #d97706;
  --table-success: #059669;
  --table-info: #0284c7;

  /* Enterprise theme fixed column shadows */
  --table-shadow-fixed-left: 2px 0 4px rgba(30, 64, 175, 0.1);
  --table-shadow-fixed-right: -2px 0 4px rgba(30, 64, 175, 0.1);
  --table-shadow-fixed-header-left: 2px 0 4px rgba(30, 64, 175, 0.15);
  --table-shadow-fixed-header-right: -2px 0 4px rgba(30, 64, 175, 0.15);
}

/* Base component styles */
.vue-table-container {
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
  color: var(--table-text);
  background-color: var(--table-background);
  transition: all var(--table-transition-duration) ease-in-out;
}

/* Theme transition styles */
.vue-table-theme-provider {
  transition:
    background-color var(--table-transition-duration) ease-in-out,
    color var(--table-transition-duration) ease-in-out;
}

/* Smooth transitions for theme changes */
.vue-table-theme-provider * {
  transition:
    background-color var(--table-transition-duration) ease-in-out,
    border-color var(--table-transition-duration) ease-in-out,
    color var(--table-transition-duration) ease-in-out,
    box-shadow var(--table-transition-duration) ease-in-out,
    opacity var(--table-transition-duration) ease-in-out;
}

/* Disable transitions when requested */
.vue-table-theme-provider.theme-transitions-disabled,
.vue-table-theme-provider.theme-transitions-disabled * {
  transition: none !important;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .vue-table-theme-provider,
  .vue-table-theme-provider * {
    transition: none !important;
  }
}

/* Enhanced theme-specific styles */
[data-theme='default'] {
  color-scheme: light;
}

[data-theme='dark'] {
  color-scheme: dark;
}

[data-theme='enterprise'] {
  color-scheme: light;
}
