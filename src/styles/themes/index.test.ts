import { describe, it, expect, beforeEach, vi } from 'vitest'
import {
  themePresets,
  defaultThemeVariables,
  darkThemeVariables,
  enterpriseThemeVariables,
  getThemePreset,
  getAvailableThemes,
  applyTheme,
  createThemeTransition
} from './index'

// Mock document
const documentMock = {
  documentElement: {
    style: {
      setProperty: vi.fn(),
      removeProperty: vi.fn()
    },
    setAttribute: vi.fn(),
    removeAttribute: vi.fn()
  }
}

Object.defineProperty(global, 'document', {
  value: documentMock,
  writable: true
})

describe('Theme System', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('theme presets', () => {
    it('should have correct number of presets', () => {
      expect(themePresets).toHaveLength(3)
    })

    it('should have all required theme names', () => {
      const themeNames = themePresets.map(preset => preset.name)
      expect(themeNames).toEqual(['default', 'dark', 'enterprise'])
    })

    it('should have display names for all presets', () => {
      themePresets.forEach(preset => {
        expect(preset.displayName).toBeTruthy()
        expect(typeof preset.displayName).toBe('string')
      })
    })

    it('should have variables for all presets', () => {
      themePresets.forEach(preset => {
        expect(preset.variables).toBeTruthy()
        expect(typeof preset.variables).toBe('object')
      })
    })
  })

  describe('theme variables', () => {
    it('should have all required default theme variables', () => {
      const requiredVars = [
        '--table-primary',
        '--table-primary-hover',
        '--table-primary-active',
        '--table-bg',
        '--table-bg-secondary',
        '--table-header-bg',
        '--table-row-hover-bg',
        '--table-row-selected-bg',
        '--table-text',
        '--table-text-secondary',
        '--table-text-disabled',
        '--table-border',
        '--table-border-light',
        '--table-success',
        '--table-warning',
        '--table-error',
        '--table-info',
        '--table-shadow',
        '--table-shadow-hover',
        '--table-padding',
        '--table-padding-sm',
        '--table-padding-lg',
        '--table-border-radius',
        '--table-border-radius-sm',
        '--table-font-size',
        '--table-font-size-sm',
        '--table-font-size-lg',
        '--table-font-weight',
        '--table-font-weight-bold',
        '--table-transition',
        '--table-transition-fast'
      ]

      requiredVars.forEach(varName => {
        expect(defaultThemeVariables[varName as keyof typeof defaultThemeVariables]).toBeTruthy()
      })
    })

    it('should have valid CSS color values', () => {
      const colorVars = [
        '--table-primary',
        '--table-bg',
        '--table-text',
        '--table-border',
        '--table-success',
        '--table-warning',
        '--table-error',
        '--table-info'
      ]

      colorVars.forEach(varName => {
        const value = defaultThemeVariables[varName as keyof typeof defaultThemeVariables]
        expect(value).toMatch(/^#[0-9a-fA-F]{6}$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/)
      })
    })

    it('should have valid CSS size values', () => {
      const sizeVars = [
        '--table-padding',
        '--table-padding-sm',
        '--table-padding-lg',
        '--table-border-radius',
        '--table-border-radius-sm',
        '--table-font-size',
        '--table-font-size-sm',
        '--table-font-size-lg'
      ]

      sizeVars.forEach(varName => {
        const value = defaultThemeVariables[varName as keyof typeof defaultThemeVariables]
        expect(value).toMatch(/^\d+(\.\d+)?(px|rem|em|%)$/)
      })
    })
  })

  describe('dark theme variables', () => {
    it('should override key color variables', () => {
      expect(darkThemeVariables['--table-primary']).toBeTruthy()
      expect(darkThemeVariables['--table-bg']).toBeTruthy()
      expect(darkThemeVariables['--table-text']).toBeTruthy()
      expect(darkThemeVariables['--table-border']).toBeTruthy()
    })

    it('should have different values from default theme', () => {
      const keyVars = ['--table-primary', '--table-bg', '--table-text', '--table-border']

      keyVars.forEach(varName => {
        const defaultValue = defaultThemeVariables[varName as keyof typeof defaultThemeVariables]
        const darkValue = darkThemeVariables[varName as keyof typeof darkThemeVariables]
        expect(darkValue).not.toBe(defaultValue)
      })
    })
  })

  describe('enterprise theme variables', () => {
    it('should override key color variables', () => {
      expect(enterpriseThemeVariables['--table-primary']).toBeTruthy()
      expect(enterpriseThemeVariables['--table-bg']).toBeTruthy()
      expect(enterpriseThemeVariables['--table-text']).toBeTruthy()
      expect(enterpriseThemeVariables['--table-border']).toBeTruthy()
    })

    it('should have different primary color from default', () => {
      const defaultPrimary = defaultThemeVariables['--table-primary']
      const enterprisePrimary = enterpriseThemeVariables['--table-primary']
      expect(enterprisePrimary).not.toBe(defaultPrimary)
    })
  })

  describe('getThemePreset', () => {
    it('should return correct preset for valid theme name', () => {
      const defaultPreset = getThemePreset('default')
      expect(defaultPreset).toBeTruthy()
      expect(defaultPreset?.name).toBe('default')
      expect(defaultPreset?.displayName).toBe('Default Light')

      const darkPreset = getThemePreset('dark')
      expect(darkPreset).toBeTruthy()
      expect(darkPreset?.name).toBe('dark')
      expect(darkPreset?.displayName).toBe('Dark')

      const enterprisePreset = getThemePreset('enterprise')
      expect(enterprisePreset).toBeTruthy()
      expect(enterprisePreset?.name).toBe('enterprise')
      expect(enterprisePreset?.displayName).toBe('Enterprise Blue')
    })

    it('should return undefined for invalid theme name', () => {
      const invalidPreset = getThemePreset('invalid' as any)
      expect(invalidPreset).toBeUndefined()
    })
  })

  describe('getAvailableThemes', () => {
    it('should return all theme names', () => {
      const themes = getAvailableThemes()
      expect(themes).toEqual(['default', 'dark', 'enterprise'])
    })

    it('should return array of strings', () => {
      const themes = getAvailableThemes()
      themes.forEach(theme => {
        expect(typeof theme).toBe('string')
      })
    })
  })

  describe('applyTheme', () => {
    it('should set data-theme attribute', () => {
      applyTheme('dark')
      expect(documentMock.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'dark')
    })

    it('should apply theme variables to document', () => {
      applyTheme('default')
      expect(documentMock.documentElement.style.setProperty).toHaveBeenCalled()

      // Check that primary color is set
      expect(documentMock.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--table-primary',
        expect.any(String)
      )
    })

    it('should apply custom variables when provided', () => {
      const customVars = {
        '--custom-color': '#ff0000',
        '--custom-size': '2rem'
      }

      applyTheme('default', customVars)

      expect(documentMock.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--custom-color',
        '#ff0000'
      )
      expect(documentMock.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--custom-size',
        '2rem'
      )
    })

    it('should handle invalid theme name gracefully', () => {
      applyTheme('invalid')
      expect(documentMock.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'invalid')
      // Should still apply default variables
      expect(documentMock.documentElement.style.setProperty).toHaveBeenCalled()
    })
  })

  describe('createThemeTransition', () => {
    it('should return CSS transition string', () => {
      const transition = createThemeTransition()
      expect(typeof transition).toBe('string')
      expect(transition).toContain('transition:')
      expect(transition).toContain('background-color')
      expect(transition).toContain('border-color')
      expect(transition).toContain('color')
      expect(transition).toContain('box-shadow')
    })

    it('should use CSS variables for transition duration', () => {
      const transition = createThemeTransition()
      expect(transition).toContain('var(--table-transition)')
    })
  })

  describe('theme consistency', () => {
    it('should have consistent variable names across themes', () => {
      const defaultKeys = Object.keys(defaultThemeVariables)
      const darkKeys = Object.keys(darkThemeVariables)
      const enterpriseKeys = Object.keys(enterpriseThemeVariables)

      // Dark and enterprise themes should only override existing variables
      darkKeys.forEach(key => {
        expect(defaultKeys).toContain(key)
      })

      enterpriseKeys.forEach(key => {
        expect(defaultKeys).toContain(key)
      })
    })

    it('should have all presets with merged variables', () => {
      themePresets.forEach(preset => {
        // Each preset should have all default variables
        const defaultKeys = Object.keys(defaultThemeVariables)
        const presetKeys = Object.keys(preset.variables)

        defaultKeys.forEach(key => {
          expect(presetKeys).toContain(key)
        })
      })
    })
  })
})