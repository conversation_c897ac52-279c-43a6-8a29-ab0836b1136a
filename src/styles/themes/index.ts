import type { ThemePreset, ThemeVariables, ThemeName } from '../../types/theme'

// Default theme variables
export const defaultThemeVariables: ThemeVariables = {
  // Primary colors
  '--table-primary': '#3b82f6',
  '--table-primary-hover': '#2563eb',
  '--table-primary-active': '#1d4ed8',

  // Background colors
  '--table-bg': '#ffffff',
  '--table-bg-secondary': '#f8fafc',
  '--table-header-bg': '#f1f5f9',
  '--table-row-hover-bg': '#f8fafc',
  '--table-row-selected-bg': '#dbeafe',

  // Text colors
  '--table-text': '#1e293b',
  '--table-text-secondary': '#64748b',
  '--table-text-disabled': '#94a3b8',

  // Border colors
  '--table-border': '#e2e8f0',
  '--table-border-light': '#f1f5f9',

  // Status colors
  '--table-success': '#10b981',
  '--table-warning': '#f59e0b',
  '--table-error': '#ef4444',
  '--table-info': '#06b6d4',

  // Shadow
  '--table-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  '--table-shadow-hover': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',

  // Spacing
  '--table-padding': '0.75rem',
  '--table-padding-sm': '0.5rem',
  '--table-padding-lg': '1rem',

  // Border radius
  '--table-border-radius': '0.375rem',
  '--table-border-radius-sm': '0.25rem',

  // Font
  '--table-font-size': '0.875rem',
  '--table-font-size-sm': '0.75rem',
  '--table-font-size-lg': '1rem',
  '--table-font-weight': '400',
  '--table-font-weight-bold': '600',

  // Transitions
  '--table-transition': 'all 150ms ease-in-out',
  '--table-transition-fast': 'all 100ms ease-in-out'
}

// Dark theme variables
export const darkThemeVariables: Partial<ThemeVariables> = {
  // Primary colors
  '--table-primary': '#60a5fa',
  '--table-primary-hover': '#3b82f6',
  '--table-primary-active': '#2563eb',

  // Background colors
  '--table-bg': '#0f172a',
  '--table-bg-secondary': '#1e293b',
  '--table-header-bg': '#334155',
  '--table-row-hover-bg': '#1e293b',
  '--table-row-selected-bg': '#1e40af',

  // Text colors
  '--table-text': '#f1f5f9',
  '--table-text-secondary': '#94a3b8',
  '--table-text-disabled': '#64748b',

  // Border colors
  '--table-border': '#334155',
  '--table-border-light': '#475569',

  // Status colors
  '--table-success': '#34d399',
  '--table-warning': '#fbbf24',
  '--table-error': '#f87171',
  '--table-info': '#22d3ee',

  // Shadow
  '--table-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3)',
  '--table-shadow-hover': '0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)'
}

// Enterprise blue theme variables
export const enterpriseThemeVariables: Partial<ThemeVariables> = {
  // Primary colors
  '--table-primary': '#1e40af',
  '--table-primary-hover': '#1d4ed8',
  '--table-primary-active': '#1e3a8a',

  // Background colors
  '--table-bg': '#f8fafc',
  '--table-bg-secondary': '#ffffff',
  '--table-header-bg': '#e2e8f0',
  '--table-row-hover-bg': '#f1f5f9',
  '--table-row-selected-bg': '#dbeafe',

  // Text colors
  '--table-text': '#0f172a',
  '--table-text-secondary': '#475569',
  '--table-text-disabled': '#64748b',

  // Border colors
  '--table-border': '#cbd5e1',
  '--table-border-light': '#e2e8f0',

  // Status colors
  '--table-success': '#059669',
  '--table-warning': '#d97706',
  '--table-error': '#dc2626',
  '--table-info': '#0284c7',

  // Shadow
  '--table-shadow': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  '--table-shadow-hover': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
}

// Theme presets
export const themePresets: ThemePreset[] = [
  {
    name: 'default',
    displayName: 'Default Light',
    variables: defaultThemeVariables
  },
  {
    name: 'dark',
    displayName: 'Dark',
    variables: {
      ...defaultThemeVariables,
      ...darkThemeVariables
    }
  },
  {
    name: 'enterprise',
    displayName: 'Enterprise Blue',
    variables: {
      ...defaultThemeVariables,
      ...enterpriseThemeVariables
    }
  }
]

// Get theme preset by name
export function getThemePreset(name: ThemeName): ThemePreset | undefined {
  return themePresets.find(preset => preset.name === name)
}

// Get all available theme names
export function getAvailableThemes(): ThemeName[] {
  return themePresets.map(preset => preset.name)
}

// Apply theme to document
export function applyTheme(themeName: ThemeName | string, customVars?: Record<string, string>) {
  const root = document.documentElement

  // Set theme data attribute
  root.setAttribute('data-theme', themeName)

  // Get theme variables
  const preset = getThemePreset(themeName as ThemeName)
  const variables = preset?.variables || defaultThemeVariables

  // Apply theme variables
  Object.entries(variables).forEach(([key, value]) => {
    root.style.setProperty(key, value)
  })

  // Apply custom variables if provided
  if (customVars) {
    Object.entries(customVars).forEach(([key, value]) => {
      root.style.setProperty(key, value)
    })
  }
}

// Create CSS class for theme transitions
export function createThemeTransition(): string {
  return `
    * {
      transition: 
        background-color var(--table-transition),
        border-color var(--table-transition),
        color var(--table-transition),
        box-shadow var(--table-transition);
    }
  `
}
