// Event system type definitions
import type {
  TableRow,
  TableColumn,
  SortConfig,
  FilterConfig,
  PaginationState,
  CellPosition
} from './table'

// Table events interface
export interface TableEvents {
  // Row events
  'row-click': (row: TableRow, index: number, event: MouseEvent) => void
  'row-dblclick': (row: TableRow, index: number, event: MouseEvent) => void
  'row-select': (selectedRows: TableRow[], row: TableRow, selected: boolean) => void
  'row-select-all': (selected: boolean, selectedRows: TableRow[], changeRows: TableRow[]) => void

  // Cell events
  'cell-click': (value: any, row: TableRow, column: TableColumn, event: MouseEvent) => void
  'cell-dblclick': (value: any, row: TableRow, column: TableColumn, event: MouseEvent) => void
  'cell-edit': (value: any, oldValue: any, row: TableRow, column: TableColumn) => void
  'cell-edit-start': (row: TableRow, column: TableColumn) => void
  'cell-edit-end': (row: TableRow, column: TableColumn, cancelled: boolean) => void

  // Data operation events
  'sort-change': (sortConfig: SortConfig[], column: TableColumn) => void
  'filter-change': (filterConfig: FilterConfig, filters: Record<string, any>) => void
  'page-change': (
    pagination: PaginationState,
    pageInfo: { current: number; pageSize: number }
  ) => void

  // Selection events
  'selection-change': (selectedRowKeys: (string | number)[], selectedRows: TableRow[]) => void

  // Scroll events
  scroll: (scrollInfo: { scrollTop: number; scrollLeft: number }) => void
  'scroll-end': (scrollInfo: { scrollTop: number; scrollLeft: number }) => void

  // Resize events
  'column-resize': (column: TableColumn, width: number) => void
  'column-resize-end': (columns: TableColumn[]) => void

  // Toolbar events
  'toolbar-action': (action: string, selectedRows: TableRow[]) => void
  'toolbar-search': (searchText: string, searchKeys: string[]) => void
  'toolbar-filter': (filters: Record<string, any>) => void
  'toolbar-export': (format: string, data: TableRow[]) => void
  'toolbar-columns': (visibleColumns: string[], columnOrder: string[]) => void

  // Keyboard events
  'keyboard-navigate': (position: CellPosition, direction: 'up' | 'down' | 'left' | 'right') => void
  'keyboard-select': (row: TableRow, selected: boolean) => void
  'keyboard-edit': (row: TableRow, column: TableColumn) => void

  // Loading and error events
  'loading-change': (loading: boolean) => void
  error: (error: Error) => void
}

// Event handler types
export type TableEventHandler<K extends keyof TableEvents> = TableEvents[K]

// Event emitter interface
export interface TableEventEmitter {
  emit<K extends keyof TableEvents>(event: K, ...args: Parameters<TableEvents[K]>): void
  on<K extends keyof TableEvents>(event: K, handler: TableEvents[K]): void
  off<K extends keyof TableEvents>(event: K, handler: TableEvents[K]): void
}
