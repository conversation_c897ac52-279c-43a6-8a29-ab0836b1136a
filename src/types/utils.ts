// Utility types for the table component

// Generic utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// Function types
export type AsyncFunction<T = any, R = any> = (arg: T) => Promise<R>
export type SyncFunction<T = any, R = any> = (arg: T) => R
export type AnyFunction<T = any, R = any> = AsyncFunction<T, R> | SyncFunction<T, R>

// Data processing types
export type DataProcessor<T = any> = (data: T[]) => T[]
export type DataFilter<T = any> = (item: T, index: number, array: T[]) => boolean
export type DataSorter<T = any> = (a: T, b: T) => number
export type DataMapper<T = any, R = any> = (item: T, index: number, array: T[]) => R

// Event types
export type EventCallback<T = any> = (event: T) => void
export type EventListener<T = any> = EventCallback<T>

// Size and position types
export interface Size {
  width: number
  height: number
}

export interface Position {
  x: number
  y: number
}

export interface Rect extends Size, Position {}

export interface Viewport {
  width: number
  height: number
  scrollTop: number
  scrollLeft: number
}

// Range types
export interface NumberRange {
  min: number
  max: number
}

export interface DateRange {
  start: Date
  end: Date
}

// Loading and error states
export interface LoadingState {
  loading: boolean
  error: Error | null
  data: any
}

export interface AsyncState<T = any> extends LoadingState {
  data: T | null
}

// Pagination types
export interface PaginationInfo {
  current: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// Selection types
export interface SelectionInfo<T = any> {
  selectedKeys: (string | number)[]
  selectedRows: T[]
  isAllSelected: boolean
  isPartialSelected: boolean
}

// Sort types
export type SortDirection = 'asc' | 'desc'
export type SortOrder = SortDirection | null

export interface SortInfo {
  column: string
  direction: SortDirection
  priority: number
}

// Filter types
export type FilterValue = string | number | boolean | Date | null | undefined
export type FilterOperator = 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn'

export interface FilterCondition {
  field: string
  operator: FilterOperator
  value: FilterValue | FilterValue[]
}

export interface FilterGroup {
  conditions: (FilterCondition | FilterGroup)[]
  operator: 'and' | 'or'
}

// Theme types
export type ColorScheme = 'light' | 'dark' | 'auto'
export type ComponentSize = 'small' | 'medium' | 'large'

// Responsive types
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
export type ResponsiveValue<T> = T | Partial<Record<Breakpoint, T>>

// Animation types
export interface TransitionConfig {
  duration: number
  easing: string
  delay?: number
}

// Keyboard types
export type KeyboardKey =
  | 'Enter'
  | 'Escape'
  | 'Space'
  | 'Tab'
  | 'ArrowUp'
  | 'ArrowDown'
  | 'ArrowLeft'
  | 'ArrowRight'
  | 'Home'
  | 'End'
  | 'PageUp'
  | 'PageDown'
  | 'Delete'
  | 'Backspace'

export interface KeyboardShortcut {
  key: KeyboardKey
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  meta?: boolean
}

// Validation types
export type ValidationRule<T = any> = (value: T) => boolean | string
export type ValidationResult = boolean | string

export interface ValidationSchema<T = any> {
  [key: string]: ValidationRule<T> | ValidationRule<T>[]
}

// Export types
export type ExportFormat = 'csv' | 'excel' | 'pdf' | 'json'
export type ExportScope = 'all' | 'selected' | 'filtered'

export interface ExportOptions {
  format: ExportFormat
  scope: ExportScope
  filename?: string
  includeHeaders?: boolean
  customFields?: string[]
}

// Plugin types
export interface PluginContext {
  config: any
  state: any
  emit: (event: string, ...args: any[]) => void
}

export interface Plugin {
  name: string
  version: string
  install: (context: PluginContext) => void
  uninstall?: (context: PluginContext) => void
}

// Performance types
export interface PerformanceMetrics {
  renderTime: number
  updateTime: number
  scrollTime: number
  memoryUsage: number
}

// Accessibility types
export interface A11yConfig {
  enabled: boolean
  announceChanges: boolean
  keyboardNavigation: boolean
  screenReaderSupport: boolean
  highContrast: boolean
}

// Internationalization types
export interface I18nConfig {
  locale: string
  messages: Record<string, string>
  dateFormat: string
  numberFormat: Intl.NumberFormatOptions
}

// Debug types
export interface DebugInfo {
  version: string
  config: any
  state: any
  performance: PerformanceMetrics
  errors: Error[]
}