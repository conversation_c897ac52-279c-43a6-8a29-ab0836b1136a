// Toolbar-related type definitions
import type { TableRow } from './table'
import type { IconName } from '../components/Icon/types'

// Toolbar configuration interface
export interface ToolbarConfig {
  title?: string | ToolbarTitleConfig // 标题配置
  actions?: ToolbarAction[] // 操作按钮配置
  search?: ToolbarSearchConfig // 搜索配置
  filter?: ToolbarFilterConfig // 过滤配置
  export?: ToolbarExportConfig // 导出配置
  columns?: ToolbarColumnsConfig // 列显示配置
  layout?: 'default' | 'compact' | 'spacious' // 布局模式
  position?: 'top' | 'bottom' | 'both' // 位置
}

// 标题配置接口
export interface ToolbarTitleConfig {
  text: string
  subtitle?: string
  icon?: IconName
  showSelectedCount?: boolean // 显示选中数量
  selectedTemplate?: string // 选中状态模板
}

// 工具栏操作按钮接口
export interface ToolbarAction {
  key: string // 操作标识
  label: string // 按钮文本
  icon?: IconName // 图标
  type?: 'primary' | 'secondary' | 'danger' | 'ghost' // 按钮类型
  disabled?: boolean | ((selectedRows: TableRow[]) => boolean) // 禁用条件
  visible?: boolean | ((selectedRows: TableRow[]) => boolean) // 显示条件
  requireSelection?: boolean // 是否需要选中行
  minSelection?: number // 最少选中数量
  maxSelection?: number // 最多选中数量
  confirm?: ToolbarActionConfirm // 确认配置
  dropdown?: ToolbarAction[] // 下拉菜单
}

// 操作确认配置
export interface ToolbarActionConfirm {
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  type?: 'warning' | 'danger' | 'info'
}

// 搜索配置接口
export interface ToolbarSearchConfig {
  enabled: boolean
  placeholder?: string
  searchKeys?: string[] // 搜索字段
  debounce?: number // 防抖延迟
  highlight?: boolean // 高亮搜索结果
  caseSensitive?: boolean // 大小写敏感
  regex?: boolean // 正则表达式支持
}

// 过滤配置接口
export interface ToolbarFilterConfig {
  enabled: boolean
  filters: ToolbarFilter[]
  mode?: 'dropdown' | 'drawer' | 'modal' // 过滤器显示模式
  showCount?: boolean // 显示过滤结果数量
}

// 过滤器接口
export interface ToolbarFilter {
  key: string // 过滤字段
  label: string // 过滤器标签
  type: 'select' | 'multiSelect' | 'dateRange' | 'numberRange' | 'text'
  options?: FilterOption[] // 选项列表
  defaultValue?: any // 默认值
}

// 过滤选项接口
export interface FilterOption {
  label: string
  value: any
  disabled?: boolean
}

// 导出配置接口
export interface ToolbarExportConfig {
  enabled: boolean
  formats: ('csv' | 'excel' | 'pdf' | 'json')[]
  filename?: string | ((data: TableRow[]) => string)
  includeHeaders?: boolean
  selectedOnly?: boolean // 仅导出选中行
  customFields?: ExportField[] // 自定义导出字段
}

// 导出字段接口
export interface ExportField {
  key: string
  label: string
  formatter?: (value: any, row: TableRow) => string
}

// 列显示配置接口
export interface ToolbarColumnsConfig {
  enabled: boolean
  mode?: 'dropdown' | 'drawer' // 显示模式
  draggable?: boolean // 支持拖拽排序
  resizable?: boolean // 支持调整宽度
  hideable?: boolean // 支持隐藏列
  fixable?: boolean // 支持固定列
}

// 工具栏状态
export interface ToolbarState {
  searchText: string // 搜索文本
  activeFilters: Record<string, unknown> // 激活的过滤器
  visibleColumns: Set<string> // 可见列
  columnOrder: string[] // 列顺序
  columnWidths: Record<string, number> // 列宽度
  isExporting: boolean // 导出状态
  selectedAction: string | null // 选中的操作
}
