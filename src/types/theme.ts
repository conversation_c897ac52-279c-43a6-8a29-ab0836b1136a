import type { ComputedRef } from 'vue'

// Theme-related type definitions

// Theme configuration interface
export interface ThemeConfig {
  name: 'default' | 'dark' | 'enterprise' | string
  customVars?: Record<string, string>
  transitions?: boolean
}

// Theme variables interface
export interface ThemeVariables {
  // Primary colors
  '--table-primary': string
  '--table-primary-hover': string
  '--table-primary-active': string

  // Background colors
  '--table-bg': string
  '--table-bg-secondary': string
  '--table-header-bg': string
  '--table-row-hover-bg': string
  '--table-row-selected-bg': string

  // Text colors
  '--table-text': string
  '--table-text-secondary': string
  '--table-text-disabled': string

  // Border colors
  '--table-border': string
  '--table-border-light': string

  // Status colors
  '--table-success': string
  '--table-warning': string
  '--table-error': string
  '--table-info': string

  // Shadow
  '--table-shadow': string
  '--table-shadow-hover': string

  // Spacing
  '--table-padding': string
  '--table-padding-sm': string
  '--table-padding-lg': string

  // Border radius
  '--table-border-radius': string
  '--table-border-radius-sm': string

  // Font
  '--table-font-size': string
  '--table-font-size-sm': string
  '--table-font-size-lg': string
  '--table-font-weight': string
  '--table-font-weight-bold': string

  // Transitions
  '--table-transition': string
  '--table-transition-fast': string
}

// Predefined theme names
export type ThemeName = 'default' | 'dark' | 'enterprise'

// Theme preset interface
export interface ThemePreset {
  name: ThemeName
  displayName: string
  variables: Partial<ThemeVariables>
}

// Theme context interface
export interface ThemeContext {
  currentTheme: ComputedRef<ThemeName | string>
  customVars: ComputedRef<Record<string, string>>
  transitionsEnabled: ComputedRef<boolean>
  isDarkTheme: ComputedRef<boolean>
  setTheme: (theme: ThemeName | string) => void
  setCustomVars: (vars: Record<string, string>) => void
  clearCustomVars: () => void
  getThemeVar: (varName: keyof ThemeVariables) => string
  toggleDarkMode: () => void
  setTransitions: (enabled: boolean) => void
}
