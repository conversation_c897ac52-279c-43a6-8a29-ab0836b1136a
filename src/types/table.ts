// Table-related type definitions
import type { VNode } from 'vue'

// Cell renderer function type
export type TableCellRenderer<T = unknown> = (props: {
  value: T
  row: TableRow
  column: TableColumn
  index: number
}) => VNode | string | number

// Cell validator function type
export type TableCellValidator<T = unknown> = (
  value: T,
  row: TableRow,
  column: TableColumn
) => boolean | string

// Column configuration interface
export interface TableColumn {
  key: string // 列标识
  title: string // 列标题
  width?: number | string // 列宽度
  minWidth?: number // 最小宽度
  maxWidth?: number // 最大宽度
  sortable?: boolean // 是否可排序
  filterable?: boolean // 是否可过滤
  editable?: boolean // 是否可编辑
  resizable?: boolean // 是否可调整大小
  fixed?: 'left' | 'right' // 固定位置
  align?: 'left' | 'center' | 'right' // 对齐方式
  render?: TableCellRenderer // 自定义渲染函数
  validator?: TableCellValidator // 数据验证函数
  template?: string // 模板字符串，支持 {{value}}, {{row.field}}, {{column.prop}} 等变量

  // 内部计算属性（运行时添加）
  _fixedLeftPosition?: number // 左固定列的位置
  _fixedRightPosition?: number // 右固定列的位置
  _fixedZIndex?: number // 固定列的z-index
  _isLastLeftFixed?: boolean // 是否是最后一个左固定列
  _isFirstRightFixed?: boolean // 是否是第一个右固定列
}

// Data row interface
export interface TableRow {
  [key: string]: any
  _id?: string | number // 唯一标识
  _selected?: boolean // 选中状态
  _editing?: boolean // 编辑状态
  _disabled?: boolean // 禁用状态
}

// Cell position interface
export interface CellPosition {
  rowIndex: number
  columnKey: string
}

// Sort configuration
export interface SortConfig {
  column: string
  direction: 'asc' | 'desc'
  priority?: number // 多列排序优先级
}

// Filter types imported from utils
import type {
  FilterOperator as UtilFilterOperator,
  FilterCondition as UtilFilterCondition,
  FilterGroup as UtilFilterGroup,
  SearchConfig as UtilSearchConfig,
  FilterResult as UtilFilterResult,
  HighlightInfo as UtilHighlightInfo
} from '@/utils/filtering'

// Re-export filter types for external use
export type FilterOperator = UtilFilterOperator
export type FilterCondition = UtilFilterCondition
export type FilterGroup = UtilFilterGroup
export type SearchConfig = UtilSearchConfig
export type FilterResult = UtilFilterResult
export type HighlightInfo = UtilHighlightInfo

// Filter configuration
export interface FilterConfig {
  search?: SearchConfig
  conditions?: FilterCondition[]
  groups?: FilterGroup[]
}

// Pagination configuration
export interface PaginationConfig {
  enabled: boolean
  pageSize: number
  currentPage: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
  pageSizeOptions?: number[]
}

// Pagination state
export interface PaginationState {
  currentPage: number
  pageSize: number
  total: number
}

// Selection configuration
export interface SelectionConfig {
  enabled: boolean
  mode: 'single' | 'multiple'
  showSelectAll?: boolean
  preserveSelectedRowKeys?: boolean
  selectedRowKeys?: (string | number)[]
  getCheckboxProps?: (row: TableRow) => { disabled?: boolean }
}

// Editing configuration
export interface EditingConfig {
  enabled: boolean
  mode: 'cell' | 'row'
  trigger: 'click' | 'dblclick'
  submitOnEnter?: boolean
  cancelOnEscape?: boolean
}

// Keyboard navigation configuration
export interface KeyboardConfig {
  enabled: boolean
  navigation?: boolean // 方向键导航
  selection?: boolean // 空格键选择
  editing?: boolean // Enter键编辑
  shortcuts?: Record<string, string> // 自定义快捷键
}

// Responsive configuration
export interface ResponsiveConfig {
  enabled: boolean
  breakpoints?: {
    xs?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  hiddenColumns?: {
    xs?: string[]
    sm?: string[]
    md?: string[]
    lg?: string[]
    xl?: string[]
  }
}

// Virtual scrolling configuration
export interface VirtualConfig {
  enabled: boolean
  threshold: number // 启用阈值
  itemHeight: number | 'auto' // 行高
  bufferSize: number // 缓冲区大小
  overscan: number // 预渲染行数
}

// Virtual scrolling state
export interface VirtualState {
  scrollTop: number // 滚动位置
  visibleStart: number // 可见开始索引
  visibleEnd: number // 可见结束索引
  totalHeight: number // 总高度
  containerHeight: number // 容器高度
}

// Table state interface
export interface TableState {
  // 数据状态
  originalData: TableRow[] // 原始数据
  processedData: TableRow[] // 处理后数据
  filteredData: TableRow[] // 过滤后数据

  // 交互状态
  selectedRows: Set<string | number> // 选中行
  editingCell: CellPosition | null // 编辑单元格
  focusedCell: CellPosition | null // 焦点单元格

  // 视图状态
  sortConfig: SortConfig[] // 排序配置（支持多列排序）
  filterConfig: FilterConfig // 过滤配置
  paginationState: PaginationState // 分页状态

  // 虚拟滚动状态
  virtualState: VirtualState // 虚拟滚动状态

  // 主题状态
  currentTheme: string // 当前主题

  // 加载状态
  loading: boolean // 加载状态
  error: Error | null // 错误状态
}
