<script setup lang="ts">
import { h, ref } from 'vue'
import VueTable from './components/Table/Table.vue'
import TailwindDemo from './playground/TailwindDemo.vue'
import type { TableConfig } from './types'

// 当前视图
const currentView = ref('table')

// 基础表格配置
const basicTableConfig: TableConfig = {
  columns: [
    {
      key: 'name',
      title: '姓名',
      width: 150,
      sortable: true,
      fixed: 'left',
      resizable: true,
      render: ({ value }) => h('span', { style: { color: 'red' } }, String(value))
    },
    {
      key: 'age',
      title: '年龄',
      width: 100,
      align: 'center',
      sortable: true,
      resizable: true,
      template: '📍 {{value}}'
    },
    { key: 'email', title: '邮箱', width: 250, sortable: true, resizable: true },
    { key: 'phone', title: '电话', width: 150, sortable: true, resizable: true },
    { key: 'department', title: '部门', width: 150, sortable: true, resizable: false },
    { key: 'position', title: '职位', width: 150, sortable: true, resizable: true },
    { key: 'salary', title: '薪资', width: 120, align: 'right', sortable: true, resizable: true },
    { key: 'hireDate', title: '入职日期', width: 150, sortable: true, resizable: true },
    { key: 'status', title: '状态', width: 100, align: 'center', fixed: 'right', resizable: true },
    { key: 'actions', title: '操作', width: 120, align: 'center', fixed: 'right', resizable: true }
  ],
  data: [
    {
      name: '张三',
      age: 30,
      email: '<EMAIL>',
      phone: '13800138001',
      department: '技术部',
      position: '前端工程师',
      salary: '¥15,000',
      hireDate: '2022-01-15',
      status: '在职',
      actions: '编辑/删除'
    },
    {
      name: '李四',
      age: 25,
      email: '<EMAIL>',
      phone: '13800138002',
      department: '产品部',
      position: '产品经理',
      salary: '¥18,000',
      hireDate: '2022-03-20',
      status: '在职',
      actions: '编辑/删除'
    },
    {
      name: '王五',
      age: 35,
      email: '<EMAIL>',
      phone: '13800138003',
      department: '技术部',
      position: '后端工程师',
      salary: '¥20,000',
      hireDate: '2021-08-10',
      status: '离职',
      actions: '查看'
    },
    {
      name: '赵六',
      age: 28,
      email: '<EMAIL>',
      phone: '13800138004',
      department: '设计部',
      position: 'UI设计师',
      salary: '¥12,000',
      hireDate: '2022-06-01',
      status: '在职',
      actions: '编辑/删除'
    },
    {
      name: '钱七',
      age: 32,
      email: '<EMAIL>',
      phone: '13800138005',
      department: '运营部',
      position: '运营专员',
      salary: '¥10,000',
      hireDate: '2021-12-15',
      status: '休假',
      actions: '编辑/删除'
    }
  ],
  bordered: true,
  striped: true,
  hoverAble: true,
  size: 'medium',
  height: '240px'
}

// 带工具栏的表格配置
const toolbarTableConfig: TableConfig = {
  columns: [
    { key: 'id', title: 'ID', width: 80, sortable: true },
    { key: 'product', title: '产品名称', width: 200, sortable: true },
    { key: 'price', title: '价格', width: 120, align: 'right', sortable: true },
    { key: 'category', title: '分类', width: 150, sortable: true },
    { key: 'stock', title: '库存', width: 100, align: 'center', sortable: true }
  ],
  data: [
    { id: 1, product: 'MacBook Pro', price: '¥12,999', category: '笔记本电脑', stock: 15 },
    { id: 2, product: 'iPhone 15', price: '¥5,999', category: '智能手机', stock: 32 },
    { id: 3, product: 'iPad Air', price: '¥4,399', category: '平板电脑', stock: 8 },
    { id: 4, product: 'Apple Watch', price: '¥2,499', category: '智能手表', stock: 25 },
    { id: 5, product: 'AirPods Pro', price: '¥1,899', category: '耳机', stock: 42 }
  ],
  toolbar: {
    title: '产品管理'
  },
  bordered: true,
  striped: false,
  hoverAble: true,
  size: 'medium'
}

// 分页表格配置
const paginationTableConfig: TableConfig = {
  columns: [
    { key: 'order', title: '订单号', width: 150, sortable: true },
    { key: 'customer', title: '客户', width: 120, sortable: true },
    { key: 'amount', title: '金额', width: 120, align: 'right', sortable: true },
    { key: 'date', title: '日期', width: 150, sortable: true },
    { key: 'status', title: '状态', width: 100, align: 'center' }
  ],
  data: [
    { order: 'ORD-001', customer: '张三', amount: '¥1,299', date: '2024-01-15', status: '已完成' },
    { order: 'ORD-002', customer: '李四', amount: '¥2,599', date: '2024-01-16', status: '处理中' },
    { order: 'ORD-003', customer: '王五', amount: '¥899', date: '2024-01-17', status: '已取消' },
    { order: 'ORD-004', customer: '赵六', amount: '¥3,299', date: '2024-01-18', status: '已完成' },
    { order: 'ORD-005', customer: '钱七', amount: '¥1,599', date: '2024-01-19', status: '待付款' }
  ],
  pagination: {
    enabled: true,
    pageSize: 3,
    currentPage: 1,
    total: 5
  },
  bordered: true,
  striped: true,
  hoverAble: true,
  size: 'medium'
}

// 空数据表格配置
const emptyTableConfig: TableConfig = {
  columns: [
    { key: 'name', title: '名称', width: 200 },
    { key: 'description', title: '描述', width: 300 },
    { key: 'status', title: '状态', width: 100 }
  ],
  data: [],
  bordered: true,
  hoverAble: true,
  size: 'medium'
}

// 加载状态
const loadingTableConfig: TableConfig = {
  columns: [
    { key: 'name', title: '名称', width: 200 },
    { key: 'description', title: '描述', width: 300 },
    { key: 'status', title: '状态', width: 100 }
  ],
  data: [
    { name: '数据1', description: '描述1', status: '正常' },
    { name: '数据2', description: '描述2', status: '正常' }
  ],
  loading: true,
  bordered: true,
  hoverAble: true,
  size: 'medium'
}

const currentDemo = ref('multi-fixed')

// 横向滚动测试配置
const scrollTableConfig: TableConfig = {
  columns: [
    { key: 'id', title: 'ID', width: 80, sortable: true, fixed: 'left' },
    { key: 'name', title: '姓名', width: 120, sortable: true, fixed: 'left' },
    { key: 'age', title: '年龄', width: 100, align: 'center', sortable: true },
    { key: 'email', title: '邮箱地址', width: 200, sortable: true },
    { key: 'phone', title: '联系电话', width: 150, sortable: true },
    { key: 'department', title: '所属部门', width: 150, sortable: true },
    { key: 'position', title: '职位名称', width: 150, sortable: true },
    { key: 'level', title: '职级', width: 100, align: 'center', sortable: true },
    { key: 'salary', title: '月薪', width: 120, align: 'right', sortable: true },
    { key: 'bonus', title: '年终奖', width: 120, align: 'right', sortable: true },
    { key: 'hireDate', title: '入职日期', width: 150, sortable: true },
    { key: 'workYears', title: '工作年限', width: 100, align: 'center', sortable: true },
    { key: 'education', title: '学历', width: 100, align: 'center', sortable: true },
    { key: 'address', title: '家庭住址', width: 200, sortable: true },
    { key: 'emergencyContact', title: '紧急联系人', width: 150, sortable: true },
    { key: 'status', title: '状态', width: 100, align: 'center', fixed: 'right' },
    { key: 'actions', title: '操作', width: 150, align: 'center', fixed: 'right' }
  ],
  data: [
    {
      id: 1,
      name: '张三',
      age: 30,
      email: '<EMAIL>',
      phone: '13800138001',
      department: '技术研发部',
      position: '高级前端工程师',
      level: 'P6',
      salary: '¥15,000',
      bonus: '¥30,000',
      hireDate: '2022-01-15',
      workYears: 5,
      education: '本科',
      address: '北京市朝阳区xxx街道xxx号',
      emergencyContact: '李某 13900139001',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 2,
      name: '李四',
      age: 25,
      email: '<EMAIL>',
      phone: '13800138002',
      department: '产品设计部',
      position: '产品经理',
      level: 'P5',
      salary: '¥18,000',
      bonus: '¥36,000',
      hireDate: '2022-03-20',
      workYears: 3,
      education: '硕士',
      address: '上海市浦东新区xxx路xxx号',
      emergencyContact: '王某 13900139002',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 3,
      name: '王五',
      age: 35,
      email: '<EMAIL>',
      phone: '13800138003',
      department: '技术研发部',
      position: '架构师',
      level: 'P8',
      salary: '¥25,000',
      bonus: '¥50,000',
      hireDate: '2021-08-10',
      workYears: 8,
      education: '硕士',
      address: '深圳市南山区xxx大道xxx号',
      emergencyContact: '赵某 13900139003',
      status: '离职',
      actions: '查看'
    },
    {
      id: 4,
      name: '刘六',
      age: 28,
      email: '<EMAIL>',
      phone: '13800138004',
      department: '市场营销部',
      position: '市场专员',
      level: 'P4',
      salary: '¥12,000',
      bonus: '¥20,000',
      hireDate: '2023-06-01',
      workYears: 2,
      education: '本科',
      address: '广州市天河区xxx路xxx号',
      emergencyContact: '陈某 13900139004',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 5,
      name: '赵七',
      age: 32,
      email: '<EMAIL>',
      phone: '13800138005',
      department: '人力资源部',
      position: 'HR专员',
      level: 'P5',
      salary: '¥14,000',
      bonus: '¥25,000',
      hireDate: '2022-09-15',
      workYears: 4,
      education: '硕士',
      address: '杭州市西湖区xxx街xxx号',
      emergencyContact: '孙某 13900139005',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 6,
      name: '孙八',
      age: 27,
      email: '<EMAIL>',
      phone: '13800138006',
      department: '财务部',
      position: '财务专员',
      level: 'P4',
      salary: '¥13,000',
      bonus: '¥22,000',
      hireDate: '2023-02-10',
      workYears: 3,
      education: '本科',
      address: '成都市锦江区xxx大道xxx号',
      emergencyContact: '周某 13900139006',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 7,
      name: '周九',
      age: 29,
      email: '<EMAIL>',
      phone: '13800138007',
      department: '技术研发部',
      position: '后端工程师',
      level: 'P5',
      salary: '¥16,000',
      bonus: '¥32,000',
      hireDate: '2022-05-20',
      workYears: 4,
      education: '本科',
      address: '武汉市武昌区xxx街xxx号',
      emergencyContact: '吴某 13900139007',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 8,
      name: '吴十',
      age: 31,
      email: '<EMAIL>',
      phone: '13800138008',
      department: '产品设计部',
      position: 'UI设计师',
      level: 'P6',
      salary: '¥17,000',
      bonus: '¥34,000',
      hireDate: '2021-11-05',
      workYears: 6,
      education: '本科',
      address: '西安市雁塔区xxx路xxx号',
      emergencyContact: '郑某 13900139008',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 9,
      name: '郑十一',
      age: 26,
      email: '<EMAIL>',
      phone: '13800138009',
      department: '运营部',
      position: '运营专员',
      level: 'P4',
      salary: '¥11,000',
      bonus: '¥18,000',
      hireDate: '2023-08-01',
      workYears: 2,
      education: '本科',
      address: '南京市建邺区xxx大道xxx号',
      emergencyContact: '冯某 13900139009',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 10,
      name: '冯十二',
      age: 33,
      email: '<EMAIL>',
      phone: '13800138010',
      department: '技术研发部',
      position: '测试工程师',
      level: 'P5',
      salary: '¥14,000',
      bonus: '¥28,000',
      hireDate: '2022-07-12',
      workYears: 5,
      education: '硕士',
      address: '重庆市渝北区xxx街xxx号',
      emergencyContact: '卫某 13900139010',
      status: '在职',
      actions: '编辑 删除'
    }
  ],
  bordered: true,
  striped: true,
  hoverAble: true,
  size: 'medium',
  stickyHeader: true,
  height: '400px'
}

// 多固定列测试配置
const multiFixedTableConfig: TableConfig = {
  columns: [
    { key: 'id', title: 'ID', width: 60, sortable: true, fixed: 'left' },
    { key: 'avatar', title: '头像', width: 80, fixed: 'left' },
    {
      key: 'name',
      title: '姓名',
      width: 100,
      sortable: true,
      fixed: 'left',
      render: ({ value }) => h('span', { style: { color: 'red' } }, String(value))
    },
    { key: 'age', title: '年龄', width: 80, align: 'center', sortable: true },
    { key: 'email', title: '邮箱地址', width: 200, sortable: true, template: '📍 {{value}}' },
    { key: 'phone', title: '联系电话', width: 150, sortable: true, template: '📞 {{value}}' },
    { key: 'department', title: '所属部门', width: 150, sortable: true },
    { key: 'position', title: '职位名称', width: 150, sortable: true },
    { key: 'level', title: '职级', width: 80, align: 'center', sortable: true },
    { key: 'salary', title: '月薪', width: 100, align: 'right', sortable: true },
    { key: 'bonus', title: '年终奖', width: 100, align: 'right', sortable: true },
    { key: 'hireDate', title: '入职日期', width: 120, sortable: true },
    { key: 'workYears', title: '工作年限', width: 100, align: 'center', sortable: true },
    { key: 'education', title: '学历', width: 80, align: 'center', sortable: true },
    { key: 'address', title: '家庭住址', width: 200, sortable: true },
    { key: 'emergencyContact', title: '紧急联系人', width: 150, sortable: true },
    { key: 'rating', title: '评级', width: 80, align: 'center', fixed: 'right' },
    { key: 'status', title: '状态', width: 80, align: 'center', fixed: 'right' },
    { key: 'actions', title: '操作', width: 120, align: 'center', fixed: 'right' }
  ],
  data: [
    {
      id: 1,
      avatar: '👨‍💻',
      name: '张三',
      age: 30,
      email: '<EMAIL>',
      phone: '13800138001',
      department: '技术研发部',
      position: '高级前端工程师',
      level: 'P6',
      salary: '¥15,000',
      bonus: '¥30,000',
      hireDate: '2022-01-15',
      workYears: 5,
      education: '本科',
      address: '北京市朝阳区xxx街道xxx号',
      emergencyContact: '李某 13900139001',
      rating: 'A',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 2,
      avatar: '👩‍💼',
      name: '李四',
      age: 25,
      email: '<EMAIL>',
      phone: '13800138002',
      department: '产品设计部',
      position: '产品经理',
      level: 'P5',
      salary: '¥18,000',
      bonus: '¥36,000',
      hireDate: '2022-03-20',
      workYears: 3,
      education: '硕士',
      address: '上海市浦东新区xxx路xxx号',
      emergencyContact: '王某 13900139002',
      rating: 'A+',
      status: '在职',
      actions: '编辑 删除'
    },
    {
      id: 3,
      avatar: '👨‍🔧',
      name: '王五',
      age: 35,
      email: '<EMAIL>',
      phone: '13800138003',
      department: '技术研发部',
      position: '架构师',
      level: 'P8',
      salary: '¥25,000',
      bonus: '¥50,000',
      hireDate: '2021-08-10',
      workYears: 8,
      education: '硕士',
      address: '深圳市南山区xxx大道xxx号',
      emergencyContact: '赵某 13900139003',
      rating: 'S',
      status: '离职',
      actions: '查看'
    }
  ],
  bordered: true,
  striped: true,
  hoverAble: true,
  size: 'medium'
}

const demos = [
  { key: 'basic', title: '基础表格', config: basicTableConfig },
  { key: 'scroll', title: '粘性标题行 & 滚动', config: scrollTableConfig },
  { key: 'multi-fixed', title: '多固定列', config: multiFixedTableConfig },
  { key: 'toolbar', title: '带工具栏', config: toolbarTableConfig },
  { key: 'pagination', title: '分页表格', config: paginationTableConfig },
  { key: 'empty', title: '空数据', config: emptyTableConfig },
  { key: 'loading', title: '加载状态', config: loadingTableConfig }
]

const getCurrentConfig = () => {
  return demos.find(demo => demo.key === currentDemo.value)?.config || basicTableConfig
}
</script>

<template>
  <div
    id="app"
    class="min-h-screen flex flex-col w-full"
  >
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b flex-shrink-0 w-full">
      <div class="w-full px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <h1 class="text-lg sm:text-xl font-semibold text-gray-900">Vue Table Component</h1>
          </div>
          <div class="flex items-center space-x-2 sm:space-x-4">
            <button
              @click="currentView = 'table'"
              :class="[
                'px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors',
                currentView === 'table'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700'
              ]"
            >
              表格演示
            </button>
            <button
              @click="currentView = 'tailwind'"
              :class="[
                'px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors',
                currentView === 'tailwind'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700'
              ]"
            >
              Tailwind 验证
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Content -->
    <main class="flex-1 overflow-auto w-full">
      <!-- Table Demo View -->
      <div
        v-if="currentView === 'table'"
        class="h-full flex flex-col w-full"
      >
        <div class="header flex-shrink-0">
          <h1>Vue Table Component 演示</h1>
          <p class="subtitle">高性能 Vue 3 表格组件，支持 TypeScript</p>
        </div>

        <div class="demo-container flex-1 overflow-auto w-full">
          <!-- 演示选择器 -->
          <div class="demo-selector">
            <h3>选择演示类型：</h3>
            <div class="demo-buttons">
              <button
                v-for="demo in demos"
                :key="demo.key"
                :class="['demo-btn', { active: currentDemo === demo.key }]"
                @click="currentDemo = demo.key"
              >
                {{ demo.title }}
              </button>
            </div>
          </div>

          <!-- 当前演示标题 -->
          <div class="current-demo w-full">
            <h2>{{ demos.find(d => d.key === currentDemo)?.title }}</h2>
          </div>

          <!-- 表格组件 -->
          <div class="table-wrapper w-full">
            <VueTable :config="getCurrentConfig()" />
          </div>

          <!-- 功能说明 -->
          <div class="features">
            <h3>已实现的功能特性：</h3>
            <div class="feature-grid">
              <div class="feature-item">
                <h4>✅ 基础渲染</h4>
                <ul>
                  <li>表格结构渲染</li>
                  <li>数据绑定</li>
                  <li>列配置</li>
                  <li>样式主题</li>
                </ul>
              </div>
              <div class="feature-item">
                <h4>✅ 表头功能</h4>
                <ul>
                  <li>列标题显示</li>
                  <li>列宽度计算</li>
                  <li>排序指示器</li>
                  <li>列对齐方式</li>
                </ul>
              </div>
              <div class="feature-item">
                <h4>✅ 表体功能</h4>
                <ul>
                  <li>数据行渲染</li>
                  <li>空状态显示</li>
                  <li>加载状态</li>
                  <li>行交互状态</li>
                </ul>
              </div>
              <div class="feature-item">
                <h4>✅ 单元格功能</h4>
                <ul>
                  <li>数据显示</li>
                  <li>自定义渲染</li>
                  <li>对齐配置</li>
                  <li>交互状态</li>
                </ul>
              </div>
              <div class="feature-item">
                <h4>✅ 状态管理</h4>
                <ul>
                  <li>错误边界</li>
                  <li>异常处理</li>
                  <li>生命周期</li>
                  <li>响应式更新</li>
                </ul>
              </div>
              <div class="feature-item">
                <h4>✅ 配置选项</h4>
                <ul>
                  <li>边框样式</li>
                  <li>斑马纹</li>
                  <li>悬停效果</li>
                  <li>尺寸大小</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 技术特性 -->
          <div class="tech-features">
            <h3>技术特性：</h3>
            <div class="tech-grid">
              <div class="tech-item">
                <span class="tech-badge">TypeScript</span>
                <p>完整的类型定义和类型安全</p>
              </div>
              <div class="tech-item">
                <span class="tech-badge">Vue 3</span>
                <p>基于 Composition API 构建</p>
              </div>
              <div class="tech-item">
                <span class="tech-badge">Tailwind CSS</span>
                <p>现代化的样式系统</p>
              </div>
              <div class="tech-item">
                <span class="tech-badge">测试覆盖</span>
                <p>136+ 测试用例，全面覆盖</p>
              </div>
              <div class="tech-item">
                <span class="tech-badge">模块化</span>
                <p>组件化架构，易于扩展</p>
              </div>
              <div class="tech-item">
                <span class="tech-badge">响应式</span>
                <p>移动端友好的设计</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tailwind Demo View -->
      <div
        v-if="currentView === 'tailwind'"
        class="h-full"
      >
        <TailwindDemo />
      </div>
    </main>
  </div>
</template>

<style scoped>
#app {
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  text-align: center;
  padding: 1rem 1rem 2rem;
  color: white;
  flex-shrink: 0;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem 2rem;
  height: 100%;
  overflow-y: auto;
}

.demo-selector {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-selector h3 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1.1rem;
}

.demo-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.demo-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  color: #6b7280;
}

.demo-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.demo-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.current-demo {
  background: white;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.current-demo h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
}

.table-wrapper {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: auto;
  max-height: 60vh;
}

.features {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.features h3 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.3rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: #f9fafb;
}

.feature-item h4 {
  margin: 0 0 0.75rem 0;
  color: #059669;
  font-size: 1rem;
}

.feature-item ul {
  margin: 0;
  padding-left: 1.2rem;
  color: #6b7280;
}

.feature-item li {
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.tech-features {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tech-features h3 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.3rem;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.tech-item {
  text-align: center;
  padding: 1rem;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.tech-badge {
  display: inline-block;
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.tech-item p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0.5rem 0.5rem 1rem;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .demo-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .feature-grid,
  .tech-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .demo-container {
    padding: 0 0.5rem 1rem;
  }

  .table-wrapper {
    padding: 0.75rem;
    border-radius: 8px;
    max-height: 50vh;
  }

  .demo-selector,
  .current-demo,
  .features,
  .tech-features {
    padding: 1rem;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.25rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }

  .table-wrapper {
    max-height: 35vh;
    padding: 0.5rem;
  }

  .feature-item,
  .tech-item {
    padding: 0.75rem;
  }

  .demo-buttons {
    justify-content: center;
    gap: 0.25rem;
  }

  .demo-btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1920px) {
  .demo-container {
    max-width: 100vw;
  }

  .table-wrapper {
    max-height: 70vh;
  }

  .feature-grid,
  .tech-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
