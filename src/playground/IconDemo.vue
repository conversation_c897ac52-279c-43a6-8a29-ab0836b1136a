<template>
  <div class="p-6 bg-gray-50 min-h-screen">
    <div class="max-w-6xl mx-auto space-y-8">
      <!-- 标题 -->
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Iconify 图标组件演示</h1>
        <p class="text-gray-600">展示项目中使用的语义化图标名称和 Iconify 图标</p>
      </div>

      <!-- 语义化图标 -->
      <section class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-semibold mb-6 text-gray-800">语义化图标</h2>

        <!-- 排序图标 -->
        <div class="mb-8">
          <h3 class="text-lg font-medium mb-4 text-gray-700">排序图标</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              v-for="iconName in sortIcons"
              :key="iconName"
              class="flex items-center justify-center p-4 border rounded-lg hover:bg-gray-50"
            >
              <div class="text-center">
                <Icon
                  :icon="iconName"
                  size="lg"
                  class="mb-2"
                />
                <div class="text-sm font-medium text-gray-900">{{ iconName }}</div>
                <div class="text-xs text-gray-500">{{ getIconMapping(iconName) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页图标 -->
        <div class="mb-8">
          <h3 class="text-lg font-medium mb-4 text-gray-700">分页图标</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              v-for="iconName in paginationIcons"
              :key="iconName"
              class="flex items-center justify-center p-4 border rounded-lg hover:bg-gray-50"
            >
              <div class="text-center">
                <Icon
                  :icon="iconName"
                  size="lg"
                  class="mb-2"
                />
                <div class="text-sm font-medium text-gray-900">{{ iconName }}</div>
                <div class="text-xs text-gray-500">{{ getIconMapping(iconName) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 通用图标 -->
        <div class="mb-8">
          <h3 class="text-lg font-medium mb-4 text-gray-700">通用图标</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div
              v-for="iconName in commonIcons"
              :key="iconName"
              class="flex items-center justify-center p-4 border rounded-lg hover:bg-gray-50"
            >
              <div class="text-center">
                <Icon
                  :icon="iconName"
                  size="lg"
                  class="mb-2"
                />
                <div class="text-sm font-medium text-gray-900">{{ iconName }}</div>
                <div class="text-xs text-gray-500">{{ getIconMapping(iconName) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主题图标 -->
        <div class="mb-8">
          <h3 class="text-lg font-medium mb-4 text-gray-700">主题图标</h3>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div
              v-for="iconName in themeIcons"
              :key="iconName"
              class="flex items-center justify-center p-4 border rounded-lg hover:bg-gray-50"
            >
              <div class="text-center">
                <Icon
                  :icon="iconName"
                  size="lg"
                  class="mb-2"
                />
                <div class="text-sm font-medium text-gray-900">{{ iconName }}</div>
                <div class="text-xs text-gray-500">{{ getIconMapping(iconName) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 工具栏图标 -->
        <div class="mb-8">
          <h3 class="text-lg font-medium mb-4 text-gray-700">工具栏图标</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div
              v-for="iconName in toolbarIcons"
              :key="iconName"
              class="flex items-center justify-center p-4 border rounded-lg hover:bg-gray-50"
            >
              <div class="text-center">
                <Icon
                  :icon="iconName"
                  size="lg"
                  class="mb-2"
                />
                <div class="text-sm font-medium text-gray-900">{{ iconName }}</div>
                <div class="text-xs text-gray-500">{{ getIconMapping(iconName) }}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 尺寸演示 -->
      <section class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-semibold mb-6 text-gray-800">图标尺寸</h2>
        <div class="grid grid-cols-3 md:grid-cols-7 gap-6">
          <div
            v-for="size in iconSizes"
            :key="size.name"
            class="text-center"
          >
            <div class="mb-2">
              <Icon
                icon="search"
                :size="size.name"
              />
            </div>
            <div class="text-sm font-medium text-gray-900">{{ size.name }}</div>
            <div class="text-xs text-gray-500">{{ size.class }}</div>
          </div>
        </div>
      </section>

      <!-- 颜色演示 -->
      <section class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-semibold mb-6 text-gray-800">图标颜色</h2>
        <div class="grid grid-cols-3 md:grid-cols-6 gap-6">
          <div
            v-for="color in iconColors"
            :key="color.name"
            class="text-center"
          >
            <div class="mb-2">
              <Icon
                icon="star"
                size="lg"
                :color="color.value"
              />
            </div>
            <div class="text-sm font-medium text-gray-900">{{ color.name }}</div>
            <div class="text-xs text-gray-500">{{ color.value }}</div>
          </div>
        </div>
      </section>

      <!-- 直接使用 Iconify 图标 -->
      <section class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-semibold mb-6 text-gray-800">直接使用 Iconify 图标</h2>
        <p class="text-gray-600 mb-4">
          你也可以直接使用 Iconify 格式的图标名称，格式为
          <code class="bg-gray-100 px-1 rounded">prefix:icon-name</code>
        </p>
        <div class="grid grid-cols-3 md:grid-cols-6 gap-6">
          <div
            v-for="iconName in directIconifyIcons"
            :key="iconName"
            class="text-center"
          >
            <div class="mb-2">
              <Icon
                :icon="iconName"
                size="lg"
              />
            </div>
            <div class="text-xs text-gray-600">{{ iconName }}</div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon, type IconSize } from '../components/Icon'

// 图标映射（从 Icon.vue 复制）
const iconMapping: Record<string, string> = {
  // 排序图标
  'sort-asc': 'lucide:chevron-up',
  'sort-desc': 'lucide:chevron-down',

  // 分页图标
  first: 'lucide:chevrons-left',
  prev: 'lucide:chevron-left',
  next: 'lucide:chevron-right',
  last: 'lucide:chevrons-right',

  // 通用图标
  empty: 'lucide:inbox',
  expand: 'lucide:chevron-right',
  collapse: 'lucide:chevron-down',

  // 主题切换图标
  light: 'lucide:sun',
  dark: 'lucide:moon',
  auto: 'lucide:monitor',

  // 工具栏图标
  create: 'lucide:plus',
  edit: 'lucide:edit-3',
  delete: 'lucide:trash-2',
  'bulk-delete': 'lucide:trash-2',
  search: 'lucide:search',
  filter: 'lucide:filter',
  refresh: 'lucide:refresh-cw',
  settings: 'lucide:settings',
  export: 'lucide:download',
  fullscreen: 'lucide:maximize',
  'fullscreen-exit': 'lucide:minimize',
  more: 'lucide:more-horizontal'
}

const getIconMapping = (iconName: string): string => {
  return iconMapping[iconName] || iconName
}

// 分类图标
const sortIcons = ['sort-asc', 'sort-desc']
const paginationIcons = ['first', 'prev', 'next', 'last']
const commonIcons = ['empty', 'expand', 'collapse']
const themeIcons = ['light', 'dark', 'auto']
const toolbarIcons = [
  'create',
  'edit',
  'delete',
  'bulk-delete',
  'search',
  'filter',
  'refresh',
  'settings',
  'export',
  'fullscreen',
  'fullscreen-exit',
  'more'
]

// 尺寸选项
const iconSizes: Array<{ name: IconSize; class: string }> = [
  { name: 'xs', class: 'w-3 h-3' },
  { name: 'sm', class: 'w-4 h-4' },
  { name: 'default', class: 'w-5 h-5' },
  { name: 'md', class: 'w-6 h-6' },
  { name: 'lg', class: 'w-7 h-7' },
  { name: 'xl', class: 'w-8 h-8' },
  { name: '2xl', class: 'w-10 h-10' }
]

// 颜色选项
const iconColors = [
  { name: 'Current', value: 'current' },
  { name: 'Blue', value: 'blue-500' },
  { name: 'Green', value: 'green-500' },
  { name: 'Red', value: 'red-500' },
  { name: 'Yellow', value: 'yellow-500' },
  { name: 'Purple', value: 'purple-500' }
]

// 直接使用的 Iconify 图标示例
const directIconifyIcons = [
  'mdi:home',
  'mdi:account',
  'mdi:heart',
  'lucide:github',
  'lucide:twitter',
  'lucide:mail'
]
</script>
