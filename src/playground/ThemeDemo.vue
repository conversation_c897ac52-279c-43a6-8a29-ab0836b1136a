<template>
  <div class="theme-demo">
    <ThemeProvider
      :theme="selectedTheme"
      :enable-transitions="enableTransitions"
      @theme-change="onThemeChange"
      @theme-ready="onThemeReady"
    >
      <template #default="{ theme, isDark, setTheme, toggleDarkMode }">
        <div class="demo-container">
          <header class="demo-header">
            <h1 class="demo-title">Vue Table Component - Theme System Demo</h1>
            <p class="demo-subtitle">
              Current Theme: {{ theme }} {{ isDark ? '(Dark Mode)' : '' }}
            </p>
          </header>

          <div class="demo-controls">
            <ThemeSwitcher
              label="Theme Selector"
              :show-dark-toggle="true"
              :show-transitions-toggle="true"
              @theme-change="onThemeSwitcherChange"
              @dark-mode-toggle="onDarkModeToggle"
              @transitions-toggle="onTransitionsToggle"
            />
          </div>

          <div class="demo-content">
            <div class="demo-card">
              <h3 class="card-title">Theme Colors</h3>
              <div class="color-grid">
                <div class="color-item">
                  <div class="color-swatch bg-table-primary"></div>
                  <span class="color-label">Primary</span>
                </div>
                <div class="color-item">
                  <div class="color-swatch bg-table-secondary"></div>
                  <span class="color-label">Secondary</span>
                </div>
                <div class="color-item">
                  <div class="color-swatch bg-table-accent"></div>
                  <span class="color-label">Accent</span>
                </div>
                <div class="color-item">
                  <div class="color-swatch bg-table-success"></div>
                  <span class="color-label">Success</span>
                </div>
                <div class="color-item">
                  <div class="color-swatch bg-table-warning"></div>
                  <span class="color-label">Warning</span>
                </div>
                <div class="color-item">
                  <div class="color-swatch bg-table-error"></div>
                  <span class="color-label">Error</span>
                </div>
              </div>
            </div>

            <div class="demo-card">
              <h3 class="card-title">Interactive Elements</h3>
              <div class="interactive-elements">
                <button
                  class="demo-button primary"
                  @click="setTheme('default')"
                >
                  Default Theme
                </button>
                <button
                  class="demo-button secondary"
                  @click="setTheme('dark')"
                >
                  Dark Theme
                </button>
                <button
                  class="demo-button accent"
                  @click="setTheme('enterprise')"
                >
                  Enterprise Theme
                </button>
                <button
                  class="demo-button"
                  @click="toggleDarkMode"
                >
                  Toggle Dark Mode
                </button>
              </div>
            </div>

            <div class="demo-card">
              <h3 class="card-title">Sample Table Preview</h3>
              <div class="sample-table">
                <div class="table-header">
                  <div class="table-cell">Name</div>
                  <div class="table-cell">Status</div>
                  <div class="table-cell">Actions</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">John Doe</div>
                  <div class="table-cell">
                    <span class="status-badge success">Active</span>
                  </div>
                  <div class="table-cell">
                    <button class="action-button">Edit</button>
                  </div>
                </div>
                <div class="table-row">
                  <div class="table-cell">Jane Smith</div>
                  <div class="table-cell">
                    <span class="status-badge warning">Pending</span>
                  </div>
                  <div class="table-cell">
                    <button class="action-button">Edit</button>
                  </div>
                </div>
                <div class="table-row">
                  <div class="table-cell">Bob Johnson</div>
                  <div class="table-cell">
                    <span class="status-badge error">Inactive</span>
                  </div>
                  <div class="table-cell">
                    <button class="action-button">Edit</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </ThemeProvider>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ThemeProvider, ThemeSwitcher } from '../components'
import type { ThemeName } from '../types/theme'

// State
const selectedTheme = ref<ThemeName>('default')
const enableTransitions = ref(true)

// Event handlers
const onThemeChange = (theme: string, previousTheme: string) => {
  console.log(`Theme changed from ${previousTheme} to ${theme}`)
}

const onThemeReady = () => {
  console.log('Theme system is ready')
}

const onThemeSwitcherChange = (theme: string) => {
  selectedTheme.value = theme as ThemeName
}

const onDarkModeToggle = (isDark: boolean) => {
  console.log(`Dark mode ${isDark ? 'enabled' : 'disabled'}`)
}

const onTransitionsToggle = (enabled: boolean) => {
  enableTransitions.value = enabled
  console.log(`Transitions ${enabled ? 'enabled' : 'disabled'}`)
}
</script>

<style scoped>
.theme-demo {
  min-height: 100vh;
  font-family:
    system-ui,
    -apple-system,
    sans-serif;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--table-text);
  margin-bottom: 0.5rem;
}

.demo-subtitle {
  font-size: 1.125rem;
  color: var(--table-text-secondary);
  margin: 0;
}

.demo-controls {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  padding: 1.5rem;
  background-color: var(--table-surface);
  border-radius: var(--table-border-radius-lg);
  border: 1px solid var(--table-border);
}

.demo-content {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.demo-card {
  background-color: var(--table-surface);
  border: 1px solid var(--table-border);
  border-radius: var(--table-border-radius-lg);
  padding: 1.5rem;
  transition: var(--table-transition);
}

.demo-card:hover {
  box-shadow: var(--table-shadow-lg);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--table-text);
  margin-bottom: 1rem;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.color-swatch {
  width: 60px;
  height: 60px;
  border-radius: var(--table-border-radius);
  border: 2px solid var(--table-border);
  transition: var(--table-transition);
}

.color-swatch:hover {
  transform: scale(1.1);
}

.color-label {
  font-size: 0.875rem;
  color: var(--table-text-secondary);
  text-align: center;
}

.interactive-elements {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.demo-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--table-border);
  border-radius: var(--table-border-radius);
  background-color: var(--table-surface);
  color: var(--table-text);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--table-transition);
}

.demo-button:hover {
  background-color: var(--table-hover);
  border-color: var(--table-primary);
}

.demo-button.primary {
  background-color: var(--table-primary);
  color: white;
  border-color: var(--table-primary);
}

.demo-button.primary:hover {
  background-color: var(--table-primary-hover);
}

.demo-button.secondary {
  background-color: var(--table-secondary);
  color: white;
  border-color: var(--table-secondary);
}

.demo-button.accent {
  background-color: var(--table-accent);
  color: white;
  border-color: var(--table-accent);
}

.sample-table {
  border: 1px solid var(--table-border);
  border-radius: var(--table-border-radius);
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 120px 100px;
  background-color: var(--table-header-bg);
  border-bottom: 1px solid var(--table-border);
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 120px 100px;
  border-bottom: 1px solid var(--table-border-light);
  transition: var(--table-transition);
}

.table-row:hover {
  background-color: var(--table-hover);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 0.75rem;
  display: flex;
  align-items: center;
  color: var(--table-text);
  font-size: 0.875rem;
}

.table-header .table-cell {
  font-weight: 600;
  color: var(--table-text);
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--table-border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.success {
  background-color: var(--table-success);
  color: white;
}

.status-badge.warning {
  background-color: var(--table-warning);
  color: white;
}

.status-badge.error {
  background-color: var(--table-error);
  color: white;
}

.action-button {
  padding: 0.25rem 0.75rem;
  border: 1px solid var(--table-border);
  border-radius: var(--table-border-radius-sm);
  background-color: var(--table-surface);
  color: var(--table-text);
  font-size: 0.75rem;
  cursor: pointer;
  transition: var(--table-transition);
}

.action-button:hover {
  background-color: var(--table-primary);
  color: white;
  border-color: var(--table-primary);
}

/* Utility classes for color swatches */
.bg-table-primary {
  background-color: var(--table-primary);
}
.bg-table-secondary {
  background-color: var(--table-secondary);
}
.bg-table-accent {
  background-color: var(--table-accent);
}
.bg-table-success {
  background-color: var(--table-success);
}
.bg-table-warning {
  background-color: var(--table-warning);
}
.bg-table-error {
  background-color: var(--table-error);
}
</style>
