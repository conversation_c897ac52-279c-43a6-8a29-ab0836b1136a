import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ThemeDemo from '../playground/ThemeDemo.vue'

// Mock document for DOM operations
const mockDocument = {
  documentElement: {
    style: {
      setProperty: vi.fn(),
      removeProperty: vi.fn()
    },
    setAttribute: vi.fn(),
    removeAttribute: vi.fn()
  },
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
  createElement: vi.fn(() => ({
    style: {},
    setAttribute: vi.fn(),
    removeAttribute: vi.fn()
  }))
}

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true
})

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

describe('Theme System Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  it('should render theme demo without errors', () => {
    expect(() => {
      mount(ThemeDemo, {
        global: {
          stubs: {
            ThemeProvider: true,
            ThemeSwitcher: true
          }
        }
      })
    }).not.toThrow()
  })

  it('should have all required theme system exports', async () => {
    const {
      ThemeProvider,
      ThemeSwitcher,
      useTheme,
      themePresets
    } = await import('../index')

    expect(ThemeProvider).toBeDefined()
    expect(ThemeSwitcher).toBeDefined()
    expect(useTheme).toBeDefined()
    expect(themePresets).toBeDefined()
  })

  it('should have correct theme presets structure', async () => {
    const { themePresets } = await import('../styles/themes')

    expect(themePresets).toHaveLength(3)

    const themeNames = themePresets.map(preset => preset.name)
    expect(themeNames).toEqual(['default', 'dark', 'enterprise'])

    themePresets.forEach(preset => {
      expect(preset).toHaveProperty('name')
      expect(preset).toHaveProperty('displayName')
      expect(preset).toHaveProperty('variables')
      expect(typeof preset.name).toBe('string')
      expect(typeof preset.displayName).toBe('string')
      expect(typeof preset.variables).toBe('object')
    })
  })

  it('should apply theme correctly', async () => {
    const { applyTheme } = await import('../styles/themes')

    applyTheme('dark')

    expect(mockDocument.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'dark')
    expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalled()
  })

  it('should handle custom theme variables', async () => {
    const { applyTheme } = await import('../styles/themes')

    const customVars = {
      '--custom-primary': '#ff0000',
      '--custom-secondary': '#00ff00'
    }

    applyTheme('default', customVars)

    expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
      '--custom-primary',
      '#ff0000'
    )
    expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
      '--custom-secondary',
      '#00ff00'
    )
  })

  it('should provide theme utilities', async () => {
    const { getThemePreset, getAvailableThemes } = await import('../styles/themes')

    const defaultPreset = getThemePreset('default')
    expect(defaultPreset).toBeDefined()
    expect(defaultPreset?.name).toBe('default')

    const availableThemes = getAvailableThemes()
    expect(availableThemes).toEqual(['default', 'dark', 'enterprise'])
  })

  it('should have consistent CSS variable naming', async () => {
    const { themePresets } = await import('../styles/themes')

    const requiredVars = [
      '--table-primary',
      '--table-bg',
      '--table-text',
      '--table-border',
      '--table-success',
      '--table-warning',
      '--table-error',
      '--table-info'
    ]

    themePresets.forEach(preset => {
      requiredVars.forEach(varName => {
        expect(preset.variables).toHaveProperty(varName)
        expect(typeof preset.variables[varName as keyof typeof preset.variables]).toBe('string')
      })
    })
  })
})