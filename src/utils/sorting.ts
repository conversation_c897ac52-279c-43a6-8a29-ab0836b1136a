// Sorting utilities for table data
import type { TableRow, TableColumn, SortConfig } from '@/types'
import { getNestedValue } from './common'

// Re-export getNestedValue for test access
export { getNestedValue }

// Data type detection
export type DataType = 'string' | 'number' | 'date' | 'boolean' | 'object'

/**
 * Detect the data type of a value
 */
export function detectDataType(value: any): DataType {
  if (value === null || value === undefined) {
    return 'string'
  }

  if (typeof value === 'boolean') {
    return 'boolean'
  }

  if (typeof value === 'number') {
    return 'number'
  }

  if (value instanceof Date) {
    return 'date'
  }

  // Check if string represents a date
  if (typeof value === 'string') {
    const dateValue = new Date(value)
    const isValidDate = !isNaN(dateValue.getTime())
    const matchesISO = value.match(/^\d{4}-\d{2}-\d{2}/)
    const matchesUS = value.match(/^\d{2}\/\d{2}\/\d{4}/)
    if (isValidDate && (matchesISO || matchesUS)) {
      return 'date'
    }

    // Check if string represents a number
    const numValue = parseFloat(value)
    if (!isNaN(numValue) && isFinite(numValue) && value.trim() === numValue.toString()) {
      return 'number'
    }

    return 'string'
  }

  return 'object'
}

/**
 * Get value from nested object path
 */
/**
 * Default comparison functions for different data types
 */
export const defaultComparators = {
  string: (a: unknown, b: unknown): number => {
    const aStr = String(a ?? '').toLowerCase()
    const bStr = String(b ?? '').toLowerCase()
    return aStr.localeCompare(bStr)
  },

  number: (a: unknown, b: unknown): number => {
    const aNum = parseFloat(String(a)) || 0
    const bNum = parseFloat(String(b)) || 0
    return aNum - bNum
  },

  date: (a: unknown, b: unknown): number => {
    const aDate = new Date(a as string | number | Date)
    const bDate = new Date(b as string | number | Date)

    if (isNaN(aDate.getTime())) return 1
    if (isNaN(bDate.getTime())) return -1

    return aDate.getTime() - bDate.getTime()
  },

  boolean: (a: unknown, b: unknown): number => {
    const aBool = Boolean(a)
    const bBool = Boolean(b)

    if (aBool === bBool) return 0
    return aBool ? 1 : -1
  },

  object: (a: unknown, b: unknown): number => {
    const aStr = JSON.stringify(a) || ''
    const bStr = JSON.stringify(b) || ''
    return aStr.localeCompare(bStr)
  }
}

/**
 * Custom sort function type
 */
export type CustomSortFunction = (a: unknown, b: unknown, column: TableColumn) => number

/**
 * Sort a single column
 */
export function sortByColumn(
  data: TableRow[],
  column: TableColumn,
  direction: 'asc' | 'desc',
  customSortFn?: CustomSortFunction
): TableRow[] {
  if (!data || data.length === 0) {
    return []
  }

  return [...data].sort((a, b) => {
    const aValue = getNestedValue(a, column.key)
    const bValue = getNestedValue(b, column.key)

    let result = 0

    // Use custom sort function if provided
    if (customSortFn) {
      result = customSortFn(aValue, bValue, column)
    } else {
      // Auto-detect data type and use appropriate comparator
      const dataType = detectDataType(aValue) || detectDataType(bValue)
      const comparator = defaultComparators[dataType]
      result = comparator(aValue, bValue)
    }

    // Handle null/undefined values - always put them at the end
    if (aValue === null || aValue === undefined) {
      return 1
    }
    if (bValue === null || bValue === undefined) {
      return -1
    }

    // Apply direction
    return direction === 'desc' ? -result : result
  })
}

/**
 * Sort by multiple columns with priority
 */
export function sortByMultipleColumns(
  data: TableRow[],
  sortConfigs: SortConfig[],
  columns: TableColumn[],
  customSortFunctions?: Record<string, CustomSortFunction>
): TableRow[] {
  if (!data || data.length === 0 || !sortConfigs || sortConfigs.length === 0) {
    return data
  }

  // Sort configs by priority (lower number = higher priority)
  const sortedConfigs = [...sortConfigs].sort((a, b) => {
    const aPriority = a.priority ?? 0
    const bPriority = b.priority ?? 0
    return aPriority - bPriority
  })

  return [...data].sort((a, b) => {
    for (const sortConfig of sortedConfigs) {
      const column = columns.find(col => col.key === sortConfig.column)
      if (!column) continue

      const aValue = getNestedValue(a, column.key)
      const bValue = getNestedValue(b, column.key)

      let result = 0
      const customSortFn = customSortFunctions?.[column.key]

      // Use custom sort function if provided
      if (customSortFn) {
        result = customSortFn(aValue, bValue, column)
      } else {
        // Auto-detect data type and use appropriate comparator
        const dataType = detectDataType(aValue) || detectDataType(bValue)
        const comparator = defaultComparators[dataType]
        result = comparator(aValue, bValue)
      }

      // Handle null/undefined values
      if (aValue === null || aValue === undefined) {
        result = 1
      } else if (bValue === null || bValue === undefined) {
        result = -1
      }

      // Apply direction
      if (sortConfig.direction === 'desc') {
        result = -result
      }

      // If values are not equal, return the result
      if (result !== 0) {
        return result
      }

      // If values are equal, continue to next sort config
    }

    return 0 // All sort configs resulted in equal values
  })
}

/**
 * Validate sort configuration
 */
export function validateSortConfig(sortConfig: SortConfig, columns: TableColumn[]): boolean {
  if (!sortConfig || typeof sortConfig !== 'object') {
    return false
  }

  if (!sortConfig.column || typeof sortConfig.column !== 'string') {
    return false
  }

  if (!['asc', 'desc'].includes(sortConfig.direction)) {
    return false
  }

  // Check if column exists and is sortable
  const column = columns.find(col => col.key === sortConfig.column)
  if (!column || column.sortable === false) {
    return false
  }

  return true
}

/**
 * Validate multiple sort configurations
 */
export function validateMultipleSortConfigs(
  sortConfigs: SortConfig[],
  columns: TableColumn[]
): boolean {
  if (!Array.isArray(sortConfigs)) {
    return false
  }

  return sortConfigs.every(config => validateSortConfig(config, columns))
}

/**
 * Get next sort direction in cycle: null -> asc -> desc -> null
 */
export function getNextSortDirection(currentDirection?: 'asc' | 'desc' | null): 'asc' | 'desc' | null {
  if (!currentDirection) {
    return 'asc'
  }
  if (currentDirection === 'asc') {
    return 'desc'
  }
  return null // Clear sort
}

/**
 * Create sort configuration with priority
 */
export function createSortConfig(
  column: string,
  direction: 'asc' | 'desc',
  priority?: number
): SortConfig {
  return {
    column,
    direction,
    priority: priority ?? 0
  }
}

/**
 * Update sort configurations for multi-column sorting
 */
export function updateSortConfigs(
  currentConfigs: SortConfig[],
  newConfig: SortConfig,
  multiSort: boolean = false
): SortConfig[] {
  if (!multiSort) {
    // Single column sorting - replace all configs
    return newConfig.direction ? [newConfig] : []
  }

  // Multi-column sorting
  const existingIndex = currentConfigs.findIndex(config => config.column === newConfig.column)

  if (newConfig.direction === null) {
    // Remove sort for this column
    return currentConfigs.filter(config => config.column !== newConfig.column)
  }

  if (existingIndex >= 0) {
    // Update existing sort config
    const updatedConfigs = [...currentConfigs]
    const existingConfig = updatedConfigs[existingIndex]
    if (existingConfig) {
      updatedConfigs[existingIndex] = { ...newConfig, priority: existingConfig.priority }
    }
    return updatedConfigs
  } else {
    // Add new sort config with next priority
    const maxPriority = Math.max(...currentConfigs.map(config => config.priority ?? 0), -1)
    return [...currentConfigs, { ...newConfig, priority: maxPriority + 1 }]
  }
}

/**
 * Get sort indicator state for a column
 */
export function getSortIndicatorState(
  column: TableColumn,
  sortConfigs: SortConfig[]
): {
  direction: 'asc' | 'desc' | null
  priority: number | null
  isActive: boolean
} {
  const sortConfig = sortConfigs.find(config => config.column === column.key)

  return {
    direction: sortConfig?.direction || null,
    priority: sortConfig?.priority ?? null,
    isActive: Boolean(sortConfig)
  }
}