// Unit tests for sorting utilities
import { describe, it, expect } from 'vitest'
import {
  detectDataType,
  getNestedValue,
  defaultComparators,
  sortByColumn,
  sortByMultipleColumns,
  validateSortConfig,
  validateMultipleSortConfigs,
  getNextSortDirection,
  updateSortConfigs,
  getSortIndicatorState,
  createSortConfig
} from './sorting'
import type { TableRow, TableColumn, SortConfig } from '@/types'

describe('sorting utilities', () => {
  // Test data
  const mockColumns: TableColumn[] = [
    { key: 'name', title: 'Name', sortable: true },
    { key: 'age', title: 'Age', sortable: true },
    { key: 'email', title: 'Email', sortable: true },
    { key: 'score', title: 'Score', sortable: true },
    { key: 'date', title: 'Date', sortable: true },
    { key: 'active', title: 'Active', sortable: true },
    { key: 'profile.city', title: 'City', sortable: true },
    { key: 'readonly', title: 'Readonly', sortable: false }
  ]

  const mockData: TableRow[] = [
    {
      name: 'Alice',
      age: 30,
      email: '<EMAIL>',
      score: 95.5,
      date: '2023-01-15',
      active: true,
      profile: { city: 'New York' }
    },
    {
      name: 'Bob',
      age: 25,
      email: '<EMAIL>',
      score: 87.2,
      date: '2023-02-20',
      active: false,
      profile: { city: 'Los Angeles' }
    },
    {
      name: 'Charlie',
      age: 35,
      email: '<EMAIL>',
      score: 92.8,
      date: '2023-01-10',
      active: true,
      profile: { city: 'Chicago' }
    },
    {
      name: null,
      age: null,
      email: null,
      score: null,
      date: null,
      active: false,
      profile: { city: null }
    }
  ]

  describe('detectDataType', () => {
    it('should detect string type', () => {
      expect(detectDataType('hello')).toBe('string')
      expect(detectDataType('')).toBe('string')
    })

    it('should detect number type', () => {
      expect(detectDataType(42)).toBe('number')
      expect(detectDataType(3.14)).toBe('number')
      expect(detectDataType('42')).toBe('number')
      expect(detectDataType('3.14')).toBe('number')
    })

    it('should detect date type', () => {
      expect(detectDataType(new Date())).toBe('date')
      expect(detectDataType('2023-01-15')).toBe('date')
      expect(detectDataType('01/15/2023')).toBe('date')
    })

    it('should detect boolean type', () => {
      expect(detectDataType(true)).toBe('boolean')
      expect(detectDataType(false)).toBe('boolean')
    })

    it('should detect object type', () => {
      expect(detectDataType({})).toBe('object')
      expect(detectDataType([])).toBe('object')
    })

    it('should handle null and undefined', () => {
      expect(detectDataType(null)).toBe('string')
      expect(detectDataType(undefined)).toBe('string')
    })
  })

  describe('getNestedValue', () => {
    const obj = {
      name: 'John',
      profile: {
        address: {
          city: 'New York'
        }
      }
    }

    it('should get simple property', () => {
      expect(getNestedValue(obj, 'name')).toBe('John')
    })

    it('should get nested property', () => {
      expect(getNestedValue(obj, 'profile.address.city')).toBe('New York')
    })

    it('should return undefined for non-existent property', () => {
      expect(getNestedValue(obj, 'nonexistent')).toBeUndefined()
      expect(getNestedValue(obj, 'profile.nonexistent')).toBeUndefined()
    })
  })

  describe('defaultComparators', () => {
    it('should compare strings correctly', () => {
      expect(defaultComparators.string('apple', 'banana')).toBeLessThan(0)
      expect(defaultComparators.string('banana', 'apple')).toBeGreaterThan(0)
      expect(defaultComparators.string('apple', 'apple')).toBe(0)
    })

    it('should compare numbers correctly', () => {
      expect(defaultComparators.number(1, 2)).toBeLessThan(0)
      expect(defaultComparators.number(2, 1)).toBeGreaterThan(0)
      expect(defaultComparators.number(1, 1)).toBe(0)
    })

    it('should compare dates correctly', () => {
      const date1 = '2023-01-01'
      const date2 = '2023-01-02'
      expect(defaultComparators.date(date1, date2)).toBeLessThan(0)
      expect(defaultComparators.date(date2, date1)).toBeGreaterThan(0)
      expect(defaultComparators.date(date1, date1)).toBe(0)
    })

    it('should compare booleans correctly', () => {
      expect(defaultComparators.boolean(false, true)).toBeLessThan(0)
      expect(defaultComparators.boolean(true, false)).toBeGreaterThan(0)
      expect(defaultComparators.boolean(true, true)).toBe(0)
    })
  })

  describe('sortByColumn', () => {
    it('should sort by string column ascending', () => {
      const column = mockColumns.find(col => col.key === 'name')!
      const result = sortByColumn(mockData, column, 'asc')

      expect(result[0].name).toBe('Alice')
      expect(result[1].name).toBe('Bob')
      expect(result[2].name).toBe('Charlie')
      expect(result[3].name).toBe(null) // null values at the end
    })

    it('should sort by string column descending', () => {
      const column = mockColumns.find(col => col.key === 'name')!
      const result = sortByColumn(mockData, column, 'desc')

      expect(result[0].name).toBe('Charlie')
      expect(result[1].name).toBe('Bob')
      expect(result[2].name).toBe('Alice')
      expect(result[3].name).toBe(null) // null values at the end
    })

    it('should sort by number column', () => {
      const column = mockColumns.find(col => col.key === 'age')!
      const result = sortByColumn(mockData, column, 'asc')

      expect(result[0].age).toBe(25)
      expect(result[1].age).toBe(30)
      expect(result[2].age).toBe(35)
      expect(result[3].age).toBe(null)
    })

    it('should sort by nested property', () => {
      const column = mockColumns.find(col => col.key === 'profile.city')!
      const result = sortByColumn(mockData, column, 'asc')

      expect(result[0].profile.city).toBe('Chicago')
      expect(result[1].profile.city).toBe('Los Angeles')
      expect(result[2].profile.city).toBe('New York')
      expect(result[3].profile.city).toBe(null)
    })

    it('should use custom sort function', () => {
      const column = mockColumns.find(col => col.key === 'name')!
      const customSort = (a: any, b: any) => {
        // Sort by string length
        const aLen = (a || '').length
        const bLen = (b || '').length
        return aLen - bLen
      }

      const result = sortByColumn(mockData, column, 'asc', customSort)
      expect(result[0].name).toBe('Bob') // shortest name
    })

    it('should handle empty data', () => {
      const column = mockColumns.find(col => col.key === 'name')!
      const result = sortByColumn([], column, 'asc')
      expect(result).toEqual([])
    })
  })

  describe('sortByMultipleColumns', () => {
    it('should sort by multiple columns with priority', () => {
      const sortConfigs: SortConfig[] = [
        { column: 'active', direction: 'desc', priority: 0 }, // Primary sort
        { column: 'age', direction: 'asc', priority: 1 }      // Secondary sort
      ]

      const result = sortByMultipleColumns(mockData, sortConfigs, mockColumns)

      // First should be active=true with lowest age
      expect(result[0].active).toBe(true)
      expect(result[0].age).toBe(30) // Alice

      // Second should be active=true with higher age
      expect(result[1].active).toBe(true)
      expect(result[1].age).toBe(35) // Charlie

      // Third should be active=false
      expect(result[2].active).toBe(false) // Bob
    })

    it('should handle empty sort configs', () => {
      const result = sortByMultipleColumns(mockData, [], mockColumns)
      expect(result).toEqual(mockData)
    })

    it('should handle invalid column in sort config', () => {
      const sortConfigs: SortConfig[] = [
        { column: 'nonexistent', direction: 'asc', priority: 0 }
      ]

      const result = sortByMultipleColumns(mockData, sortConfigs, mockColumns)
      expect(result).toEqual(mockData) // Should return original data
    })
  })

  describe('validateSortConfig', () => {
    it('should validate correct sort config', () => {
      const config: SortConfig = { column: 'name', direction: 'asc' }
      expect(validateSortConfig(config, mockColumns)).toBe(true)
    })

    it('should reject invalid direction', () => {
      const config = { column: 'name', direction: 'invalid' } as any
      expect(validateSortConfig(config, mockColumns)).toBe(false)
    })

    it('should reject non-existent column', () => {
      const config: SortConfig = { column: 'nonexistent', direction: 'asc' }
      expect(validateSortConfig(config, mockColumns)).toBe(false)
    })

    it('should reject non-sortable column', () => {
      const config: SortConfig = { column: 'readonly', direction: 'asc' }
      expect(validateSortConfig(config, mockColumns)).toBe(false)
    })

    it('should reject invalid config object', () => {
      expect(validateSortConfig(null as any, mockColumns)).toBe(false)
      expect(validateSortConfig({} as any, mockColumns)).toBe(false)
    })
  })

  describe('validateMultipleSortConfigs', () => {
    it('should validate array of correct configs', () => {
      const configs: SortConfig[] = [
        { column: 'name', direction: 'asc' },
        { column: 'age', direction: 'desc' }
      ]
      expect(validateMultipleSortConfigs(configs, mockColumns)).toBe(true)
    })

    it('should reject array with invalid config', () => {
      const configs = [
        { column: 'name', direction: 'asc' },
        { column: 'nonexistent', direction: 'desc' }
      ] as SortConfig[]
      expect(validateMultipleSortConfigs(configs, mockColumns)).toBe(false)
    })

    it('should reject non-array input', () => {
      expect(validateMultipleSortConfigs({} as any, mockColumns)).toBe(false)
    })
  })

  describe('getNextSortDirection', () => {
    it('should cycle through sort directions', () => {
      expect(getNextSortDirection()).toBe('asc')
      expect(getNextSortDirection('asc')).toBe('desc')
      expect(getNextSortDirection('desc')).toBe(null)
      expect(getNextSortDirection(null)).toBe('asc')
    })
  })

  describe('updateSortConfigs', () => {
    it('should replace all configs in single sort mode', () => {
      const currentConfigs: SortConfig[] = [
        { column: 'name', direction: 'asc', priority: 0 }
      ]
      const newConfig: SortConfig = { column: 'age', direction: 'desc', priority: 0 }

      const result = updateSortConfigs(currentConfigs, newConfig, false)
      expect(result).toEqual([newConfig])
    })

    it('should add new config in multi sort mode', () => {
      const currentConfigs: SortConfig[] = [
        { column: 'name', direction: 'asc', priority: 0 }
      ]
      const newConfig: SortConfig = { column: 'age', direction: 'desc', priority: 1 }

      const result = updateSortConfigs(currentConfigs, newConfig, true)
      expect(result).toHaveLength(2)
      expect(result[1].priority).toBe(1)
    })

    it('should update existing config in multi sort mode', () => {
      const currentConfigs: SortConfig[] = [
        { column: 'name', direction: 'asc', priority: 0 },
        { column: 'age', direction: 'asc', priority: 1 }
      ]
      const newConfig: SortConfig = { column: 'age', direction: 'desc', priority: 1 }

      const result = updateSortConfigs(currentConfigs, newConfig, true)
      expect(result).toHaveLength(2)
      expect(result[1].direction).toBe('desc')
    })

    it('should remove config when direction is null', () => {
      const currentConfigs: SortConfig[] = [
        { column: 'name', direction: 'asc', priority: 0 },
        { column: 'age', direction: 'desc', priority: 1 }
      ]
      const newConfig: SortConfig = { column: 'age', direction: null as any, priority: 1 }

      const result = updateSortConfigs(currentConfigs, newConfig, true)
      expect(result).toHaveLength(1)
      expect(result[0].column).toBe('name')
    })
  })

  describe('getSortIndicatorState', () => {
    it('should return correct state for sorted column', () => {
      const sortConfigs: SortConfig[] = [
        { column: 'name', direction: 'asc', priority: 0 }
      ]
      const column = mockColumns.find(col => col.key === 'name')!

      const state = getSortIndicatorState(column, sortConfigs)
      expect(state.direction).toBe('asc')
      expect(state.priority).toBe(0)
      expect(state.isActive).toBe(true)
    })

    it('should return correct state for unsorted column', () => {
      const sortConfigs: SortConfig[] = []
      const column = mockColumns.find(col => col.key === 'name')!

      const state = getSortIndicatorState(column, sortConfigs)
      expect(state.direction).toBe(null)
      expect(state.priority).toBe(null)
      expect(state.isActive).toBe(false)
    })
  })

  describe('createSortConfig', () => {
    it('should create sort config with default priority', () => {
      const config = createSortConfig('name', 'asc')
      expect(config).toEqual({
        column: 'name',
        direction: 'asc',
        priority: 0
      })
    })

    it('should create sort config with custom priority', () => {
      const config = createSortConfig('name', 'desc', 5)
      expect(config).toEqual({
        column: 'name',
        direction: 'desc',
        priority: 5
      })
    })
  })
})