// Type validation and default value utilities
import type {
  TableColumn,
  TableRow,
  PaginationConfig,
  SelectionConfig,
  EditingConfig,
  KeyboardConfig,
  ResponsiveConfig,
  VirtualConfig
} from '../types/table'
import type { TableConfig } from '../types'
import type { ToolbarConfig } from '../types/toolbar'
import type { ThemeConfig } from '../types/theme'

// Default configurations
export const DEFAULT_PAGINATION_CONFIG: Required<PaginationConfig> = {
  enabled: true,
  pageSize: 20,
  currentPage: 1,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: true,
  pageSizeOptions: [10, 20, 50, 100]
}

export const DEFAULT_SELECTION_CONFIG: Required<SelectionConfig> = {
  enabled: false,
  mode: 'multiple',
  showSelectAll: true,
  preserveSelectedRowKeys: false,
  selectedRowKeys: [],
  getCheckboxProps: () => ({})
}

export const DEFAULT_EDITING_CONFIG: Required<EditingConfig> = {
  enabled: false,
  mode: 'cell',
  trigger: 'dblclick',
  submitOnEnter: true,
  cancelOnEscape: true
}

export const DEFAULT_KEYBOARD_CONFIG: Required<KeyboardConfig> = {
  enabled: true,
  navigation: true,
  selection: true,
  editing: true,
  shortcuts: {
    'ctrl+a': 'selectAll',
    'ctrl+c': 'copy',
    delete: 'delete',
    enter: 'edit',
    escape: 'cancel'
  }
}

export const DEFAULT_RESPONSIVE_CONFIG: Required<ResponsiveConfig> = {
  enabled: true,
  breakpoints: {
    xs: 480,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200
  },
  hiddenColumns: {}
}

export const DEFAULT_VIRTUAL_CONFIG: Required<VirtualConfig> = {
  enabled: false,
  threshold: 100,
  itemHeight: 54,
  bufferSize: 10,
  overscan: 5
}

export const DEFAULT_THEME_CONFIG: Required<ThemeConfig> = {
  name: 'default',
  customVars: {},
  transitions: true
}

export const DEFAULT_TOOLBAR_CONFIG: Partial<ToolbarConfig> = {
  layout: 'default',
  position: 'top'
}

// Validation functions
export function validateTableColumn(column: any): column is TableColumn {
  if (!column || typeof column !== 'object') {
    return false
  }

  if (typeof column.key !== 'string' || !column.key.trim()) {
    return false
  }

  if (typeof column.title !== 'string' || !column.title.trim()) {
    return false
  }

  // Optional field validations
  if (
    column.width !== undefined &&
    typeof column.width !== 'number' &&
    typeof column.width !== 'string'
  ) {
    return false
  }

  if (
    column.minWidth !== undefined &&
    (typeof column.minWidth !== 'number' || column.minWidth < 0)
  ) {
    return false
  }

  if (
    column.maxWidth !== undefined &&
    (typeof column.maxWidth !== 'number' || column.maxWidth < 0)
  ) {
    return false
  }

  if (column.fixed !== undefined && !['left', 'right'].includes(column.fixed)) {
    return false
  }

  if (column.align !== undefined && !['left', 'center', 'right'].includes(column.align)) {
    return false
  }

  return true
}

export function validateTableRow(row: any): row is TableRow {
  return Boolean(row && typeof row === 'object' && !Array.isArray(row))
}

export function validateTableConfig(config: any): config is TableConfig {
  if (!config || typeof config !== 'object') {
    return false
  }

  // Validate columns
  if (!Array.isArray(config.columns) || config.columns.length === 0) {
    return false
  }

  if (!config.columns.every(validateTableColumn)) {
    return false
  }

  // Validate data
  if (!Array.isArray(config.data)) {
    return false
  }

  if (!config.data.every(validateTableRow)) {
    return false
  }

  return true
}

// Default value functions
export function getDefaultTableColumn(column: Partial<TableColumn>): TableColumn {
  return {
    key: column.key || '',
    title: column.title || '',
    width: column.width,
    minWidth: column.minWidth || 80,
    maxWidth: column.maxWidth,
    sortable: column.sortable ?? true,
    filterable: column.filterable ?? true,
    editable: column.editable ?? false,
    fixed: column.fixed,
    align: column.align || 'left',
    render: column.render,
    validator: column.validator
  }
}

export function getDefaultTableRow(row: Partial<TableRow>): TableRow {
  return {
    ...row,
    _id: row._id || Math.random().toString(36).substr(2, 9),
    _selected: row._selected ?? false,
    _editing: row._editing ?? false,
    _disabled: row._disabled ?? false
  }
}

export function getDefaultPaginationConfig(config?: Partial<PaginationConfig>): PaginationConfig {
  return {
    ...DEFAULT_PAGINATION_CONFIG,
    ...config
  }
}

export function getDefaultSelectionConfig(config?: Partial<SelectionConfig>): SelectionConfig {
  return {
    ...DEFAULT_SELECTION_CONFIG,
    ...config
  }
}

export function getDefaultEditingConfig(config?: Partial<EditingConfig>): EditingConfig {
  return {
    ...DEFAULT_EDITING_CONFIG,
    ...config
  }
}

export function getDefaultKeyboardConfig(config?: Partial<KeyboardConfig>): KeyboardConfig {
  return {
    ...DEFAULT_KEYBOARD_CONFIG,
    ...config,
    shortcuts: {
      ...DEFAULT_KEYBOARD_CONFIG.shortcuts,
      ...config?.shortcuts
    }
  }
}

export function getDefaultResponsiveConfig(config?: Partial<ResponsiveConfig>): ResponsiveConfig {
  return {
    ...DEFAULT_RESPONSIVE_CONFIG,
    ...config,
    breakpoints: {
      ...DEFAULT_RESPONSIVE_CONFIG.breakpoints,
      ...config?.breakpoints
    },
    hiddenColumns: {
      ...DEFAULT_RESPONSIVE_CONFIG.hiddenColumns,
      ...config?.hiddenColumns
    }
  }
}

export function getDefaultVirtualConfig(config?: Partial<VirtualConfig>): VirtualConfig {
  return {
    ...DEFAULT_VIRTUAL_CONFIG,
    ...config
  }
}

export function getDefaultThemeConfig(config?: Partial<ThemeConfig>): ThemeConfig {
  return {
    ...DEFAULT_THEME_CONFIG,
    ...config,
    customVars: {
      ...DEFAULT_THEME_CONFIG.customVars,
      ...config?.customVars
    }
  }
}

export function getDefaultToolbarConfig(config?: Partial<ToolbarConfig>): ToolbarConfig {
  return {
    ...DEFAULT_TOOLBAR_CONFIG,
    ...config
  }
}

export function getDefaultTableConfig(config: Partial<TableConfig>): TableConfig {
  const processedColumns =
    config.columns?.map((col: Partial<TableColumn>) => getDefaultTableColumn(col)) || []
  const processedData = config.data?.map((row: Partial<TableRow>) => getDefaultTableRow(row)) || []

  return {
    columns: processedColumns,
    data: processedData,
    toolbar: config.toolbar ? getDefaultToolbarConfig(config.toolbar) : undefined,
    theme: getDefaultThemeConfig(config.theme),
    virtual: getDefaultVirtualConfig(config.virtual),
    pagination: config.pagination ? getDefaultPaginationConfig(config.pagination) : undefined,
    selection: config.selection ? getDefaultSelectionConfig(config.selection) : undefined,
    editing: config.editing ? getDefaultEditingConfig(config.editing) : undefined,
    keyboard: getDefaultKeyboardConfig(config.keyboard),
    responsive: getDefaultResponsiveConfig(config.responsive),
    loading: config.loading ?? false,
    height: config.height,
    maxHeight: config.maxHeight,
    bordered: config.bordered ?? true,
    striped: config.striped ?? false,
    hoverAble: config.hoverAble ?? true,
    size: config.size || 'medium'
  }
}

// Type guards
export function isTableColumn(value: any): value is TableColumn {
  return validateTableColumn(value)
}

export function isTableRow(value: any): value is TableRow {
  return validateTableRow(value)
}

export function isTableConfig(value: any): value is TableConfig {
  return validateTableConfig(value)
}

// Error classes for validation
export class TableValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public value?: any
  ) {
    super(message)
    this.name = 'TableValidationError'
  }
}

export class TableConfigError extends Error {
  constructor(
    message: string,
    public config?: any
  ) {
    super(message)
    this.name = 'TableConfigError'
  }
}

// Validation with error throwing
export function validateAndThrow(config: unknown): asserts config is TableConfig {
  if (!validateTableConfig(config)) {
    throw new TableConfigError('Invalid table configuration', config)
  }
}
