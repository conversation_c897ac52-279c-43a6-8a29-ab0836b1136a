// Common utility functions shared across the table library

/**
 * Get nested value from object using dot notation path
 * @param obj - The object to get value from
 * @param path - Dot notation path (e.g., 'user.profile.name')
 * @returns The nested value or undefined if not found
 */
export function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

/**
 * Format value as string for display
 * @param value - Value to format
 * @returns Formatted string
 */
export function formatValue(value: any): string {
  if (value === null || value === undefined) return ''
  if (typeof value === 'boolean') return value ? 'true' : 'false'
  if (typeof value === 'object') return JSON.stringify(value)
  return String(value)
}