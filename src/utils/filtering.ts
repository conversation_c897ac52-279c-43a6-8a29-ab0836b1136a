// Filtering utilities for table data
import type { TableRow, TableColumn } from '@/types'
import { getNestedValue } from './common'

// Re-export getNestedValue for test access
export { getNestedValue }

// Filter types
export type FilterOperator =
  | 'equals'
  | 'notEquals'
  | 'contains'
  | 'notContains'
  | 'startsWith'
  | 'endsWith'
  | 'greaterThan'
  | 'lessThan'
  | 'greaterThanOrEqual'
  | 'lessThanOrEqual'
  | 'between'
  | 'in'
  | 'notIn'
  | 'isEmpty'
  | 'isNotEmpty'
  | 'regex'

// Filter condition interface
export interface FilterCondition {
  column: string
  operator: FilterOperator
  value: unknown
  caseSensitive?: boolean
}

// Filter group interface for complex filtering
export interface FilterGroup {
  conditions: (FilterCondition | FilterGroup)[]
  operator: 'and' | 'or'
}

// Search configuration
export interface SearchConfig {
  text: string
  columns?: string[] // Specific columns to search, if empty search all filterable columns
  caseSensitive?: boolean
  regex?: boolean
  highlight?: boolean
}

// Filter result with highlighting information
export interface FilterResult {
  data: TableRow[]
  highlightInfo?: Map<string, HighlightInfo[]> // row id -> highlight info
}

export interface HighlightInfo {
  column: string
  matches: Array<{ start: number; end: number; text: string }>
}

/**
 * Get value from nested object path
 */
/**
 * Convert value to string for comparison
 */
export function valueToString(value: any): string {
  if (value === null || value === undefined) {
    return ''
  }
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

/**
 * Apply a single filter condition to a row
 */
export function applyFilterCondition(row: TableRow, condition: FilterCondition): boolean {
  const value = getNestedValue(row, condition.column)
  const filterValue = condition.value

  // Handle empty checks first
  if (condition.operator === 'isEmpty') {
    return value === null || value === undefined || valueToString(value).trim() === ''
  }

  if (condition.operator === 'isNotEmpty') {
    return value !== null && value !== undefined && valueToString(value).trim() !== ''
  }

  // Convert values to strings for text-based operations
  let valueStr = valueToString(value)
  let filterStr = valueToString(filterValue)

  // Apply case sensitivity
  if (!condition.caseSensitive) {
    valueStr = valueStr.toLowerCase()
    filterStr = filterStr.toLowerCase()
  }

  switch (condition.operator) {
    case 'equals':
      return valueStr === filterStr

    case 'notEquals':
      return valueStr !== filterStr

    case 'contains':
      return valueStr.includes(filterStr)

    case 'notContains':
      return !valueStr.includes(filterStr)

    case 'startsWith':
      return valueStr.startsWith(filterStr)

    case 'endsWith':
      return valueStr.endsWith(filterStr)

    case 'greaterThan':
      return Number(value) > Number(filterValue)

    case 'lessThan':
      return Number(value) < Number(filterValue)

    case 'greaterThanOrEqual':
      return Number(value) >= Number(filterValue)

    case 'lessThanOrEqual':
      return Number(value) <= Number(filterValue)

    case 'between':
      if (Array.isArray(filterValue) && filterValue.length === 2) {
        const numValue = Number(value)
        return numValue >= Number(filterValue[0]) && numValue <= Number(filterValue[1])
      }
      return false

    case 'in':
      if (Array.isArray(filterValue)) {
        return filterValue.some(fv => valueToString(fv).toLowerCase() === valueStr)
      }
      return false

    case 'notIn':
      if (Array.isArray(filterValue)) {
        return !filterValue.some(fv => valueToString(fv).toLowerCase() === valueStr)
      }
      return true

    case 'regex':
      try {
        const regex = new RegExp(filterStr, condition.caseSensitive ? 'g' : 'gi')
        return regex.test(valueStr)
      } catch {
        return false
      }

    default:
      return true
  }
}

/**
 * Apply filter group (complex filtering with AND/OR logic)
 */
export function applyFilterGroup(row: TableRow, group: FilterGroup): boolean {
  if (!group.conditions || group.conditions.length === 0) {
    return true
  }

  const results = group.conditions.map(condition => {
    if ('conditions' in condition) {
      // Nested filter group
      return applyFilterGroup(row, condition)
    } else {
      // Single filter condition
      return applyFilterCondition(row, condition)
    }
  })

  return group.operator === 'and' ? results.every(result => result) : results.some(result => result)
}

/**
 * Simple text search across multiple columns
 */
export function applyTextSearch(
  data: TableRow[],
  searchConfig: SearchConfig,
  columns: TableColumn[]
): FilterResult {
  if (!searchConfig.text.trim()) {
    return { data }
  }

  const searchText = searchConfig.text.trim()
  const searchColumns =
    searchConfig.columns || columns.filter(col => col.filterable !== false).map(col => col.key)

  const highlightInfo = new Map<string, HighlightInfo[]>()

  const filteredData = data.filter(row => {
    const rowId = String(row._id || '')
    const rowHighlights: HighlightInfo[] = []

    let hasMatch = false

    for (const columnKey of searchColumns) {
      const value = getNestedValue(row, columnKey)
      let valueStr = valueToString(value)

      if (!searchConfig.caseSensitive) {
        valueStr = valueStr.toLowerCase()
      }

      let searchPattern = searchText
      if (!searchConfig.caseSensitive) {
        searchPattern = searchPattern.toLowerCase()
      }

      const matches: Array<{ start: number; end: number; text: string }> = []

      if (searchConfig.regex) {
        try {
          const flags = searchConfig.caseSensitive ? 'g' : 'gi'
          const regex = new RegExp(searchPattern, flags)
          let match

          while ((match = regex.exec(valueStr)) !== null) {
            matches.push({
              start: match.index,
              end: match.index + match[0].length,
              text: match[0]
            })
            hasMatch = true
          }
        } catch {
          // Invalid regex, fall back to simple contains
          if (valueStr.includes(searchPattern)) {
            const index = valueStr.indexOf(searchPattern)
            matches.push({
              start: index,
              end: index + searchPattern.length,
              text: searchPattern
            })
            hasMatch = true
          }
        }
      } else {
        // Simple text search
        if (valueStr.includes(searchPattern)) {
          let startIndex = 0
          let index

          while ((index = valueStr.indexOf(searchPattern, startIndex)) !== -1) {
            matches.push({
              start: index,
              end: index + searchPattern.length,
              text: searchPattern
            })
            startIndex = index + 1
            hasMatch = true
          }
        }
      }

      if (matches.length > 0 && searchConfig.highlight) {
        rowHighlights.push({
          column: columnKey,
          matches
        })
      }
    }

    if (hasMatch && searchConfig.highlight && rowHighlights.length > 0) {
      highlightInfo.set(rowId, rowHighlights)
    }

    return hasMatch
  })

  return {
    data: filteredData,
    highlightInfo: searchConfig.highlight ? highlightInfo : undefined
  }
}

/**
 * Apply complex filtering with conditions and groups
 */
export function applyComplexFilter(data: TableRow[], filterGroup: FilterGroup): TableRow[] {
  if (!filterGroup.conditions || filterGroup.conditions.length === 0) {
    return data
  }

  return data.filter(row => applyFilterGroup(row, filterGroup))
}

/**
 * Combine multiple filters (search + complex filters)
 */
export function applyAllFilters(
  data: TableRow[],
  searchConfig?: SearchConfig,
  filterGroup?: FilterGroup,
  columns: TableColumn[] = []
): FilterResult {
  let result = data

  // Apply complex filters first
  if (filterGroup) {
    result = applyComplexFilter(result, filterGroup)
  }

  // Apply text search
  if (searchConfig && searchConfig.text.trim()) {
    const searchResult = applyTextSearch(result, searchConfig, columns)
    return searchResult
  }

  return { data: result }
}

/**
 * Create a simple filter condition
 */
export function createFilterCondition(
  column: string,
  operator: FilterOperator,
  value: any,
  caseSensitive: boolean = false
): FilterCondition {
  return {
    column,
    operator,
    value,
    caseSensitive
  }
}

/**
 * Create a filter group
 */
export function createFilterGroup(
  conditions: (FilterCondition | FilterGroup)[],
  operator: 'and' | 'or' = 'and'
): FilterGroup {
  return {
    conditions,
    operator
  }
}

/**
 * Validate filter condition
 */
export function validateFilterCondition(
  condition: FilterCondition,
  columns: TableColumn[]
): boolean {
  if (!condition || typeof condition !== 'object') {
    return false
  }

  if (!condition.column || typeof condition.column !== 'string') {
    return false
  }

  if (!condition.operator) {
    return false
  }

  // Check if column exists and is filterable
  const column = columns.find(col => col.key === condition.column)
  if (!column || column.filterable === false) {
    return false
  }

  // Validate operator-specific requirements
  switch (condition.operator) {
    case 'between':
      return Array.isArray(condition.value) && condition.value.length === 2
    case 'in':
    case 'notIn':
      return Array.isArray(condition.value)
    case 'isEmpty':
    case 'isNotEmpty':
      return true // No value required
    default:
      return condition.value !== undefined
  }
}

/**
 * Validate filter group recursively
 */
export function validateFilterGroup(group: FilterGroup, columns: TableColumn[]): boolean {
  if (!group || typeof group !== 'object') {
    return false
  }

  if (!Array.isArray(group.conditions)) {
    return false
  }

  if (!['and', 'or'].includes(group.operator)) {
    return false
  }

  return group.conditions.every(condition => {
    if ('conditions' in condition) {
      return validateFilterGroup(condition, columns)
    } else {
      return validateFilterCondition(condition, columns)
    }
  })
}

/**
 * Get filterable columns from column configuration
 */
export function getFilterableColumns(columns: TableColumn[]): TableColumn[] {
  return columns.filter(column => column.filterable !== false)
}

/**
 * Extract unique values from a column for filter options
 */
export function getColumnUniqueValues(
  data: TableRow[],
  columnKey: string,
  limit: number = 100
): any[] {
  const values = new Set()

  for (const row of data) {
    const value = getNestedValue(row, columnKey)
    if (value !== null && value !== undefined) {
      values.add(value)
      if (values.size >= limit) break
    }
  }

  return Array.from(values).sort()
}

/**
 * Highlight text matches in a string
 */
export function highlightMatches(
  text: string,
  matches: Array<{ start: number; end: number; text: string }>,
  highlightClass: string = 'highlight'
): string {
  if (!matches || matches.length === 0) {
    return text
  }

  // Sort matches by start position (descending) to avoid index shifting
  const sortedMatches = [...matches].sort((a, b) => b.start - a.start)

  let result = text

  for (const match of sortedMatches) {
    const before = result.substring(0, match.start)
    const highlighted = `<span class="${highlightClass}">${match.text}</span>`
    const after = result.substring(match.end)
    result = before + highlighted + after
  }

  return result
}
