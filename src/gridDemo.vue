<template>
  <VueTable
    :config="tableConfig"
    class="w-[900px]"
  />
</template>

<script setup lang="ts">
import { h, ref } from 'vue'
import { VueTable, type TableConfig } from '@happy-table/vue3'
import '@happy-table/vue3/components.css'

const tableConfig = ref<TableConfig>({
  columns: [
    {
      key: 'name',
      title: '姓名',
      width: 150,
      sortable: true,
      fixed: 'left',
      resizable: true,
      render: ({ value }) => h('span', { style: { color: 'red' } }, String(value))
    },
    {
      key: 'age',
      title: '年龄',
      width: 100,
      align: 'center',
      sortable: true,
      resizable: true,
      template: '📍 {{value}}'
    },
    { key: 'email', title: '邮箱', width: 250, sortable: true, resizable: true },
    { key: 'phone', title: '电话', width: 150, sortable: true, resizable: true },
    { key: 'department', title: '部门', width: 150, sortable: true, resizable: false },
    { key: 'position', title: '职位', width: 150, sortable: true, resizable: true },
    { key: 'salary', title: '薪资', width: 120, align: 'right', sortable: true, resizable: true },
    { key: 'hireDate', title: '入职日期', width: 150, sortable: true, resizable: true },
    { key: 'status', title: '状态', width: 100, align: 'center', fixed: 'right', resizable: true },
    { key: 'actions', title: '操作', width: 120, align: 'center', fixed: 'right', resizable: true }
  ],
  data: [
    {
      name: '张三',
      age: 30,
      email: '<EMAIL>',
      phone: '13800138001',
      department: '技术部',
      position: '前端工程师',
      salary: '¥15,000',
      hireDate: '2022-01-15',
      status: '在职',
      actions: '编辑/删除'
    },
    {
      name: '李四',
      age: 25,
      email: '<EMAIL>',
      phone: '13800138002',
      department: '产品部',
      position: '产品经理',
      salary: '¥18,000',
      hireDate: '2022-03-20',
      status: '在职',
      actions: '编辑/删除'
    },
    {
      name: '王五',
      age: 35,
      email: '<EMAIL>',
      phone: '13800138003',
      department: '技术部',
      position: '后端工程师',
      salary: '¥20,000',
      hireDate: '2021-08-10',
      status: '离职',
      actions: '查看'
    },
    {
      name: '赵六',
      age: 28,
      email: '<EMAIL>',
      phone: '13800138004',
      department: '设计部',
      position: 'UI设计师',
      salary: '¥12,000',
      hireDate: '2022-06-01',
      status: '在职',
      actions: '编辑/删除'
    },
    {
      name: '钱七',
      age: 32,
      email: '<EMAIL>',
      phone: '13800138005',
      department: '运营部',
      position: '运营专员',
      salary: '¥10,000',
      hireDate: '2021-12-15',
      status: '休假',
      actions: '编辑/删除'
    }
  ],
  bordered: true,
  striped: true,
  stickyHeader: true,
  hoverAble: true,
  size: 'medium',
  height: '240px'
})
</script>
