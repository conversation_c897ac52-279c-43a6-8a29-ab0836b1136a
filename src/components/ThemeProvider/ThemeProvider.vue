<template>
  <div
    :data-theme="currentTheme"
    :class="[
      'vue-table-theme-provider',
      {
        'theme-transitions-enabled': transitionsEnabled,
        'theme-transitions-disabled': !transitionsEnabled
      }
    ]"
  >
    <slot
      :theme="currentTheme"
      :set-theme="setTheme"
      :toggle-dark-mode="toggleDarkMode"
      :is-dark="isDarkTheme"
      :custom-vars="customVars"
      :set-custom-vars="setCustomVars"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch } from 'vue'
import { useTheme } from '../../composables/useTheme'
import type { ThemeConfig, ThemeName } from '../../types/theme'

// Props
interface Props {
  theme?: ThemeName | string
  config?: ThemeConfig
  enableTransitions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'default',
  enableTransitions: true
})

// Emits
interface Emits {
  (e: 'theme-change', theme: string, previousTheme: string): void
  (e: 'theme-ready'): void
}

const emit = defineEmits<Emits>()

// Use theme composable
const {
  currentTheme,
  customVars,
  transitionsEnabled,
  isDarkTheme,
  setTheme,
  setCustomVars,
  toggleDarkMode,
  setTransitions
} = useTheme()

// Watch for prop changes
watch(
  () => props.theme,
  newTheme => {
    if (newTheme && newTheme !== currentTheme.value) {
      setTheme(newTheme)
    }
  },
  { immediate: true }
)

watch(
  () => props.config,
  newConfig => {
    if (newConfig) {
      if (newConfig.name && newConfig.name !== currentTheme.value) {
        setTheme(newConfig.name)
      }
      if (newConfig.customVars) {
        setCustomVars(newConfig.customVars)
      }
      if (typeof newConfig.transitions === 'boolean') {
        setTransitions(newConfig.transitions)
      }
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => props.enableTransitions,
  enabled => {
    setTransitions(enabled)
  },
  { immediate: true }
)

// Theme change event handler
const handleThemeChange = (event: CustomEvent) => {
  const { theme, previousTheme } = event.detail
  emit('theme-change', theme, previousTheme)
}

// Lifecycle
onMounted(() => {
  // Listen for theme change events
  document.addEventListener('theme-change', handleThemeChange as EventListener)

  // Emit ready event
  emit('theme-ready')
})

onUnmounted(() => {
  document.removeEventListener('theme-change', handleThemeChange as EventListener)
})

// Expose methods for template ref access
defineExpose({
  setTheme,
  toggleDarkMode,
  setCustomVars,
  currentTheme,
  isDarkTheme
})
</script>

<style scoped>
.vue-table-theme-provider {
  /* Base theme provider styles */
  color: var(--table-text);
  background-color: var(--table-bg);
}

.theme-transitions-enabled {
  transition: var(--table-transition);
}

.theme-transitions-enabled * {
  transition:
    background-color var(--table-transition),
    border-color var(--table-transition),
    color var(--table-transition),
    box-shadow var(--table-transition),
    opacity var(--table-transition);
}

.theme-transitions-disabled,
.theme-transitions-disabled * {
  transition: none !important;
}

/* Smooth theme transition animations */
@media (prefers-reduced-motion: no-preference) {
  .theme-transitions-enabled {
    transition-duration: 200ms;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .theme-transitions-enabled,
  .theme-transitions-enabled * {
    transition-duration: 0ms !important;
  }
}
</style>
