import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick, ref } from 'vue'
import ThemeProvider from './ThemeProvider.vue'

// Mock the useTheme composable
const mockCurrentTheme = ref('default')
const mockCustomVars = ref({})
const mockTransitionsEnabled = ref(true)
const mockIsDarkTheme = ref(false)

const mockUseTheme = {
  currentTheme: mockCurrentTheme,
  customVars: mockCustomVars,
  transitionsEnabled: mockTransitionsEnabled,
  isDarkTheme: mockIsDarkTheme,
  setTheme: vi.fn(),
  setCustomVars: vi.fn(),
  toggleDarkMode: vi.fn(),
  setTransitions: vi.fn()
}

vi.mock('../../composables/useTheme', () => ({
  useTheme: () => mockUseTheme
}))

// Spy on document methods instead of replacing the entire document object
const documentAddEventListener = vi.spyOn(document, 'addEventListener')
const documentRemoveEventListener = vi.spyOn(document, 'removeEventListener')
const documentDispatchEvent = vi.spyOn(document, 'dispatchEvent')

describe('ThemeProvider', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock ref values
    mockCurrentTheme.value = 'default'
    mockCustomVars.value = {}
    mockTransitionsEnabled.value = true
    mockIsDarkTheme.value = false
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('rendering', () => {
    it('should render with default theme', () => {
      const wrapper = mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      expect(wrapper.find('.vue-table-theme-provider').exists()).toBe(true)
      expect(wrapper.attributes('data-theme')).toBe('default')
      expect(wrapper.text()).toContain('Test content')
    })

    it('should apply theme class based on current theme', async () => {
      mockCurrentTheme.value = 'dark'

      const wrapper = mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      expect(wrapper.attributes('data-theme')).toBe('dark')
    })

    it('should apply transitions class when enabled', () => {
      mockTransitionsEnabled.value = true

      const wrapper = mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      expect(wrapper.classes()).toContain('theme-transitions-enabled')
      expect(wrapper.classes()).not.toContain('theme-transitions-disabled')
    })

    it('should apply disabled transitions class when disabled', () => {
      mockTransitionsEnabled.value = false

      const wrapper = mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      expect(wrapper.classes()).toContain('theme-transitions-disabled')
      expect(wrapper.classes()).not.toContain('theme-transitions-enabled')
    })
  })

  describe('props', () => {
    it('should set theme from props', async () => {
      mount(ThemeProvider, {
        props: {
          theme: 'dark'
        },
        slots: {
          default: '<div>Test content</div>'
        }
      })

      await nextTick()
      expect(mockUseTheme.setTheme).toHaveBeenCalledWith('dark')
    })

    it('should apply config from props', async () => {
      const config = {
        name: 'enterprise',
        customVars: { '--custom-color': '#ff0000' },
        transitions: false
      }

      mount(ThemeProvider, {
        props: {
          config
        },
        slots: {
          default: '<div>Test content</div>'
        }
      })

      await nextTick()
      expect(mockUseTheme.setTheme).toHaveBeenCalledWith('enterprise')
      expect(mockUseTheme.setCustomVars).toHaveBeenCalledWith({ '--custom-color': '#ff0000' })
      expect(mockUseTheme.setTransitions).toHaveBeenCalledWith(false)
    })

    it('should set transitions from enableTransitions prop', async () => {
      mount(ThemeProvider, {
        props: {
          enableTransitions: false
        },
        slots: {
          default: '<div>Test content</div>'
        }
      })

      await nextTick()
      expect(mockUseTheme.setTransitions).toHaveBeenCalledWith(false)
    })
  })

  describe('slots', () => {
    it('should provide theme data to scoped slot', () => {
      const wrapper = mount(ThemeProvider, {
        slots: {
          default: `
            <template #default="{ theme, setTheme, toggleDarkMode, isDark, customVars, setCustomVars }">
              <div>
                <span class="theme">{{ theme }}</span>
                <span class="is-dark">{{ isDark }}</span>
                <button @click="setTheme('dark')">Set Dark</button>
                <button @click="toggleDarkMode">Toggle</button>
                <button @click="setCustomVars({ '--test': '#fff' })">Set Vars</button>
              </div>
            </template>
          `
        }
      })

      expect(wrapper.find('.theme').text()).toBe('default')
      expect(wrapper.find('.is-dark').text()).toBe('false')
    })

    it('should call theme methods from scoped slot', async () => {
      const wrapper = mount(ThemeProvider, {
        slots: {
          default: `
            <template #default="{ setTheme, toggleDarkMode, setCustomVars }">
              <div>
                <button class="set-theme" @click="setTheme('dark')">Set Dark</button>
                <button class="toggle" @click="toggleDarkMode">Toggle</button>
                <button class="set-vars" @click="setCustomVars({ '--test': '#fff' })">Set Vars</button>
              </div>
            </template>
          `
        }
      })

      await wrapper.find('.set-theme').trigger('click')
      expect(mockUseTheme.setTheme).toHaveBeenCalledWith('dark')

      await wrapper.find('.toggle').trigger('click')
      expect(mockUseTheme.toggleDarkMode).toHaveBeenCalled()

      await wrapper.find('.set-vars').trigger('click')
      expect(mockUseTheme.setCustomVars).toHaveBeenCalledWith({ '--test': '#fff' })
    })
  })

  describe('events', () => {
    it('should emit theme-ready on mount', () => {
      const wrapper = mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      expect(wrapper.emitted('theme-ready')).toBeTruthy()
    })

    it('should listen for theme change events', () => {
      mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      expect(documentAddEventListener).toHaveBeenCalledWith('theme-change', expect.any(Function))
    })

    it('should emit theme-change when theme changes', async () => {
      const wrapper = mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      // Simulate theme change event
      const themeChangeEvent = new CustomEvent('theme-change', {
        detail: { theme: 'dark', previousTheme: 'default' }
      })

      // Get the event handler that was registered
      const eventHandler = documentAddEventListener.mock.calls.find(
        call => call[0] === 'theme-change'
      )?.[1]

      if (eventHandler) {
        eventHandler(themeChangeEvent)
        await nextTick()

        expect(wrapper.emitted('theme-change')).toBeTruthy()
        expect(wrapper.emitted('theme-change')?.[0]).toEqual(['dark', 'default'])
      }
    })

    it('should remove event listener on unmount', () => {
      const wrapper = mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      wrapper.unmount()

      expect(documentRemoveEventListener).toHaveBeenCalledWith('theme-change', expect.any(Function))
    })
  })

  describe('exposed methods', () => {
    it('should expose theme methods via template ref', () => {
      const wrapper = mount(ThemeProvider, {
        slots: {
          default: '<div>Test content</div>'
        }
      })

      const vm = wrapper.vm as any
      expect(vm.setTheme).toBe(mockUseTheme.setTheme)
      expect(vm.toggleDarkMode).toBe(mockUseTheme.toggleDarkMode)
      expect(vm.setCustomVars).toBe(mockUseTheme.setCustomVars)
      // Vue auto-unwraps refs in component instances
      expect(vm.currentTheme).toBe(mockCurrentTheme.value)
      expect(vm.isDarkTheme).toBe(mockIsDarkTheme.value)
    })
  })

  describe('prop reactivity', () => {
    it('should react to theme prop changes', async () => {
      const wrapper = mount(ThemeProvider, {
        props: {
          theme: 'default'
        },
        slots: {
          default: '<div>Test content</div>'
        }
      })

      await wrapper.setProps({ theme: 'dark' })
      expect(mockUseTheme.setTheme).toHaveBeenCalledWith('dark')
    })

    it('should react to config prop changes', async () => {
      const wrapper = mount(ThemeProvider, {
        props: {
          config: {
            name: 'default',
            customVars: {},
            transitions: true
          }
        },
        slots: {
          default: '<div>Test content</div>'
        }
      })

      await wrapper.setProps({
        config: {
          name: 'enterprise',
          customVars: { '--new-color': '#blue' },
          transitions: false
        }
      })

      expect(mockUseTheme.setTheme).toHaveBeenCalledWith('enterprise')
      expect(mockUseTheme.setCustomVars).toHaveBeenCalledWith({ '--new-color': '#blue' })
      expect(mockUseTheme.setTransitions).toHaveBeenCalledWith(false)
    })

    it('should react to enableTransitions prop changes', async () => {
      const wrapper = mount(ThemeProvider, {
        props: {
          enableTransitions: true
        },
        slots: {
          default: '<div>Test content</div>'
        }
      })

      await wrapper.setProps({ enableTransitions: false })
      expect(mockUseTheme.setTransitions).toHaveBeenCalledWith(false)
    })
  })
})
