<template>
  <div
    class="table-cell"
    :class="cellClasses"
    :style="cellStyle"
    @click="handleClick"
    @dblclick="handleDoubleClick"
  >
    <!-- Editing mode -->
    <div
      v-if="editing"
      class="cell-editor"
    >
      <input
        v-if="getEditorType() === 'input'"
        ref="editorRef"
        v-model="editValue"
        class="cell-input"
        :type="getInputType()"
        @blur="handleEditorBlur"
        @keydown="handleEditorKeydown"
      />
      <textarea
        v-else-if="getEditorType() === 'textarea'"
        ref="editorRef"
        v-model="editValue"
        class="cell-textarea"
        @blur="handleEditorBlur"
        @keydown="handleEditorKeydown"
      />
      <select
        v-else-if="getEditorType() === 'select'"
        ref="editorRef"
        v-model="editValue"
        class="cell-select"
        @blur="handleEditorBlur"
        @keydown="handleEditorKeydown"
      >
        <option
          v-for="option in getSelectOptions()"
          :key="String(option.value)"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </option>
      </select>
    </div>

    <!-- Display mode -->
    <div
      v-else
      class="cell-content"
    >
      <slot
        :value="value"
        :row="row"
        :column="column"
        :index="index"
      >
        <!-- Custom render function, template, or default display -->
        <component
          v-if="column.render"
          :is="renderComponent"
        />
        <span
          v-else-if="column.template"
          class="cell-text"
          v-html="templateValue"
        />
        <span
          v-else
          class="cell-text"
          >{{ displayValue }}</span
        >
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, watch } from 'vue'
import type { TableColumn, TableRow as TableRowType } from '@/types'
import { renderTemplate } from '@/utils/template'

// Props definition
interface Props {
  column: TableColumn
  row: TableRowType
  value: any
  index: number
  selected?: boolean
  disabled?: boolean
  editing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  disabled: false,
  editing: false
})

// Emits definition
interface Emits {
  'cell-click': [column: TableColumn, value: any, event: MouseEvent]
  'cell-dblclick': [column: TableColumn, value: any, event: MouseEvent]
  'cell-edit': [column: TableColumn, value: any]
  'cell-change': [column: TableColumn, oldValue: unknown, newValue: unknown]
}

const emit = defineEmits<Emits>()

// Template refs
const editorRef = ref<HTMLElement>()

// Internal state
const editValue = ref(props.value)

// Computed properties
const cellClasses = computed(() => {
  const classes = ['cell']

  if (props.column.align) classes.push(`cell-align-${props.column.align}`)
  if (props.column.fixed) classes.push(`cell-fixed-${props.column.fixed}`)
  if (props.column._isLastLeftFixed) classes.push('cell-last-left-fixed')
  if (props.column._isFirstRightFixed) classes.push('cell-first-right-fixed')
  if (props.selected) classes.push('cell-selected')
  if (props.disabled) classes.push('cell-disabled')
  if (props.editing) classes.push('cell-editing')
  if (props.column.editable) classes.push('cell-editable')

  return classes
})

const cellStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.column.width) {
    style['width'] =
      typeof props.column.width === 'number' ? `${props.column.width}px` : props.column.width
    style['minWidth'] = style['width']
    style['maxWidth'] = style['width']
  }

  if (props.column.minWidth) {
    style['minWidth'] = `${props.column.minWidth}px`
  }

  if (props.column.maxWidth) {
    style['maxWidth'] = `${props.column.maxWidth}px`
  }

  if (props.column.align) {
    style['textAlign'] = props.column.align
  }

  // Fixed column positioning
  if (props.column.fixed === 'left') {
    style['position'] = 'sticky'
    style['left'] = `${props.column._fixedLeftPosition || 0}px`
    style['zIndex'] = String(props.column._fixedZIndex || 10)
  } else if (props.column.fixed === 'right') {
    style['position'] = 'sticky'
    style['right'] = `${props.column._fixedRightPosition || 0}px`
    style['zIndex'] = String(props.column._fixedZIndex || 10)
  }

  return style
})

const displayValue = computed(() => {
  if (props.value === null || props.value === undefined) {
    return ''
  }

  if (typeof props.value === 'boolean') {
    return props.value ? '是' : '否'
  }

  if (typeof props.value === 'object') {
    return JSON.stringify(props.value)
  }

  return String(props.value)
})

const templateValue = computed(() => {
  if (!props.column.template) return ''

  try {
    return renderTemplate(props.column.template, {
      value: props.value,
      row: props.row,
      column: props.column,
      index: props.index
    })
  } catch (error) {
    console.warn('模板渲染失败:', error)
    return String(props.value || '')
  }
})

const renderComponent = computed(() => {
  if (!props.column.render) return null

  // 返回一个函数式组件，直接渲染 VNode
  return () => {
    return props.column.render!({
      value: props.value,
      row: props.row,
      column: props.column,
      index: props.index
    })
  }
})

// Methods
const getEditorType = (): string => {
  // This could be extended to support different editor types based on column configuration
  if (typeof props.value === 'boolean') return 'select'
  if (typeof props.value === 'number') return 'input'
  if (typeof props.value === 'string' && props.value.length > 50) return 'textarea'
  return 'input'
}

const getInputType = (): string => {
  if (typeof props.value === 'number') return 'number'
  if (typeof props.value === 'string') {
    // Simple email detection
    if (props.value.includes('@')) return 'email'
    // Simple URL detection
    if (props.value.startsWith('http')) return 'url'
  }
  return 'text'
}

const getSelectOptions = () => {
  if (typeof props.value === 'boolean') {
    return [
      { label: '是', value: true, disabled: false },
      { label: '否', value: false, disabled: false }
    ]
  }
  return []
}

const handleClick = (event: MouseEvent): void => {
  if (props.disabled) return
  emit('cell-click', props.column, props.value, event)
}

const handleDoubleClick = (event: MouseEvent): void => {
  if (props.disabled) return
  if (props.column.editable) {
    emit('cell-edit', props.column, props.value)
  }
  emit('cell-dblclick', props.column, props.value, event)
}

const handleEditorBlur = (): void => {
  if (editValue.value !== props.value) {
    emit('cell-change', props.column, props.value, editValue.value)
  }
}

const handleEditorKeydown = (event: KeyboardEvent): void => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    if (editorRef.value) {
      editorRef.value.blur()
    }
  } else if (event.key === 'Escape') {
    event.preventDefault()
    editValue.value = props.value // Reset to original value
    if (editorRef.value) {
      editorRef.value.blur()
    }
  }
}

// Watchers
watch(
  () => props.value,
  newValue => {
    editValue.value = newValue
  }
)

watch(
  () => props.editing,
  async isEditing => {
    if (isEditing) {
      await nextTick()
      if (editorRef.value) {
        editorRef.value.focus()
        if (editorRef.value && 'select' in editorRef.value) {
          ;(editorRef.value as HTMLInputElement).select()
        }
      }
    }
  }
)
</script>

<style scoped>
@reference "../../styles/base.css";

.table-cell {
  @apply px-4 py-3 text-table-text border-r border-b border-table-border last:border-r-0 flex-shrink-0 transition-colors;
}

.cell-align-center {
  @apply text-center;
}

.cell-align-right {
  @apply text-right;
}

.cell-selected {
  @apply bg-table-row-selected-bg;
}

.cell-disabled {
  @apply opacity-50 cursor-not-allowed;
}

.cell-editing {
  @apply bg-table-primary/10 ring-1 ring-table-primary/30;
}

.cell-editable {
  @apply cursor-pointer;
}

.cell-editable:hover:not(.cell-disabled) {
  @apply bg-table-row-hover-bg;
}

.cell-fixed-left {
  @apply bg-table-bg;
  /* 确保固定列在滚动时有正确的背景 */
  background-color: var(--table-background) !important;
  /* 默认不显示阴影，只在最后一个左固定列显示 */
}

.cell-fixed-right {
  @apply bg-table-bg;
  /* 确保固定列在滚动时有正确的背景 */
  background-color: var(--table-background) !important;
  /* 默认不显示阴影，只在第一个右固定列显示 */
}

/* 修复滚动时的样式问题 */
.table-cell {
  /* 确保单元格在滚动时保持正确的边框 */
  border-right: 1px solid var(--table-border);
  /* 防止内容溢出 */
  overflow: hidden;
}

.table-cell:last-child {
  border-right: none;
}

/* 固定列的特殊处理 */
.cell-fixed-left,
.cell-fixed-right {
  /* 确保固定列始终有边框 */
  border-right: 1px solid var(--table-border) !important;
}

/* 当表格有横向滚动时的样式优化 */
.table-cell.cell-fixed-left {
  /* 左固定列的右边框 */
  border-right: 1px solid var(--table-border) !important;
  /* 确保z-index足够高，多个固定列时使用递增的z-index */
  z-index: 10 !important;
}

.table-cell.cell-fixed-right {
  /* 右固定列的左边框 */
  border-left: 1px solid var(--table-border) !important;
  /* 确保z-index足够高，多个固定列时使用递增的z-index */
  z-index: 10 !important;
}

/* z-index通过JavaScript动态计算和设置 */

/* 悬停状态下的固定列样式 */
.cell-fixed-left:hover,
.cell-fixed-right:hover {
  background-color: var(--table-hover) !important;
}

/* 选中状态下的固定列样式 */
.cell-fixed-left.cell-selected,
.cell-fixed-right.cell-selected {
  background-color: var(--table-selected) !important;
}

/* 当整行悬停时，固定列也应该有悬停效果 */
.table-row:hover .cell-fixed-left,
.table-row:hover .cell-fixed-right {
  background-color: var(--table-hover) !important;
}

/* 当整行被选中时，固定列也应该有选中效果 */
.table-row.row-selected .cell-fixed-left,
.table-row.row-selected .cell-fixed-right {
  background-color: var(--table-selected) !important;
}

/* 斑马纹行的固定列样式 */
.table-row.row-striped .cell-fixed-left,
.table-row.row-striped .cell-fixed-right {
  background-color: var(--table-surface) !important;
}

/* 斑马纹行悬停时的固定列样式 */
.table-row.row-striped:hover .cell-fixed-left,
.table-row.row-striped:hover .cell-fixed-right {
  background-color: var(--table-hover) !important;
}

/* 边界固定列的阴影分隔效果 */
.cell-last-left-fixed {
  /* 最后一个左固定列显示右侧阴影，分隔固定区域和滚动区域 */
  box-shadow: var(--table-shadow-fixed-left) !important;
}

.cell-first-right-fixed {
  /* 第一个右固定列显示左侧阴影，分隔滚动区域和固定区域 */
  box-shadow: var(--table-shadow-fixed-right) !important;
}

/* Cell content styles */
.cell-content {
  @apply w-full;
}

.cell-text {
  @apply truncate block;
}

/* Editor styles */
.cell-editor {
  @apply w-full;
}

.cell-input,
.cell-textarea,
.cell-select {
  @apply w-full px-2 py-1 text-sm border border-table-border rounded bg-table-bg text-table-text;
  @apply focus:outline-none focus:ring-2 focus:ring-table-primary focus:border-table-primary;
}

.cell-textarea {
  @apply resize-none min-h-[2rem];
}

.cell-select {
  @apply cursor-pointer;
}

/* Focus styles */
.table-cell:focus {
  @apply outline-none ring-2 ring-table-primary ring-offset-1 ring-offset-table-bg;
}

.table-cell:focus-visible {
  @apply outline-none ring-2 ring-table-primary ring-offset-1 ring-offset-table-bg;
}

/* Responsive design */
@media (max-width: 768px) {
  .table-cell {
    @apply px-2 py-2 text-sm;
  }

  .cell-input,
  .cell-textarea,
  .cell-select {
    @apply text-xs px-1 py-0.5;
  }
}
</style>
