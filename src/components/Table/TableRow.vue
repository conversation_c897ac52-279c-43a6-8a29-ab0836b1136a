<template>
  <div
    class="table-row"
    :class="rowClasses"
    :style="rowStyle"
    @click="handleClick"
    @dblclick="handleDoubleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @contextmenu="handleContextMenu"
  >
    <TableCell
      v-for="column in columns"
      :key="column.key"
      :column="column"
      :row="row"
      :value="getCellValue(column)"
      :index="index"
      :selected="selected"
      :disabled="disabled"
      :editing="editing && isColumnEditable(column)"
      @cell-click="handleCellClick"
      @cell-dblclick="handleCellDoubleClick"
      @cell-edit="handleCellEdit"
      @cell-change="handleCellChange"
    >
      <!-- Pass through cell slots -->
      <template #default="cellProps">
        <slot
          :name="`cell-${column.key}`"
          v-bind="cellProps"
        >
          <!-- 如果列有自定义render函数或template模板，不提供默认内容，让TableCell处理 -->
          <template v-if="!column.render && !column.template">
            <!-- Default cell content -->
            {{ getCellDisplayValue(column) }}
          </template>
        </slot>
      </template>
    </TableCell>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import TableCell from './TableCell.vue'
import type { TableColumn, TableRow as TableRowType } from '@/types'

// Props definition
interface Props {
  row: TableRowType
  columns: TableColumn[]
  index: number
  selected?: boolean
  disabled?: boolean
  editing?: boolean
  striped?: boolean
  hoverAble?: boolean
  rowHeight?: number | string
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  disabled: false,
  editing: false,
  striped: false,
  hoverAble: true
})

// Emits definition
interface Emits {
  click: [event: MouseEvent]
  dblclick: [event: MouseEvent]
  mouseenter: [event: MouseEvent]
  mouseleave: [event: MouseEvent]
  contextmenu: [event: MouseEvent]
  'cell-click': [column: TableColumn, value: any, event: MouseEvent]
  'cell-dblclick': [column: TableColumn, value: any, event: MouseEvent]
  'cell-edit': [column: TableColumn, value: any]
  'cell-change': [column: TableColumn, oldValue: any, newValue: any]
}

const emit = defineEmits<Emits>()

// Computed properties
const rowClasses = computed(() => {
  const classes = ['row']

  if (props.selected) classes.push('row-selected')
  if (props.disabled) classes.push('row-disabled')
  if (props.editing) classes.push('row-editing')
  if (props.striped) classes.push('row-striped')
  if (props.hoverAble && !props.disabled) classes.push('row-hoverAble')

  return classes
})

const rowStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.rowHeight) {
    style['height'] = typeof props.rowHeight === 'number' ? `${props.rowHeight}px` : props.rowHeight
    style['minHeight'] = style['height']
  }

  return style
})

// Methods
const getCellValue = (column: TableColumn): any => {
  return props.row[column.key]
}

const getCellDisplayValue = (column: TableColumn): string => {
  const value = getCellValue(column)

  if (value === null || value === undefined) {
    return ''
  }

  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  if (typeof value === 'object') {
    return JSON.stringify(value)
  }

  return String(value)
}

const isColumnEditable = (column: TableColumn): boolean => {
  return column.editable === true
}

const handleClick = (event: MouseEvent): void => {
  if (props.disabled) return
  emit('click', event)
}

const handleDoubleClick = (event: MouseEvent): void => {
  if (props.disabled) return
  emit('dblclick', event)
}

const handleMouseEnter = (event: MouseEvent): void => {
  emit('mouseenter', event)
}

const handleMouseLeave = (event: MouseEvent): void => {
  emit('mouseleave', event)
}

const handleContextMenu = (event: MouseEvent): void => {
  if (props.disabled) return
  emit('contextmenu', event)
}

const handleCellClick = (column: TableColumn, value: any, event: MouseEvent): void => {
  emit('cell-click', column, value, event)
}

const handleCellDoubleClick = (column: TableColumn, value: any, event: MouseEvent): void => {
  emit('cell-dblclick', column, value, event)
}

const handleCellEdit = (column: TableColumn, value: any): void => {
  emit('cell-edit', column, value)
}

const handleCellChange = (column: TableColumn, oldValue: any, newValue: any): void => {
  emit('cell-change', column, oldValue, newValue)
}
</script>

<style scoped>
@reference "../../styles/base.css";

.table-row {
  @apply flex transition-colors duration-150;
}

.row-selected {
  @apply bg-table-row-selected-bg;
}

.row-disabled {
  @apply opacity-50 cursor-not-allowed;
}

.row-editing {
  @apply bg-table-primary/5 ring-1 ring-table-primary/20;
}

.row-striped {
  @apply bg-table-bg-secondary;
}

.row-hoverAble:hover:not(.row-disabled) {
  @apply bg-table-row-hover-bg;
}

/* Selected row hover state */
.row-selected.row-hoverAble:hover:not(.row-disabled) {
  @apply bg-table-row-selected-bg brightness-95;
}

/* Editing row hover state */
.row-editing.row-hoverAble:hover:not(.row-disabled) {
  @apply bg-table-primary/10;
}

/* Focus styles for keyboard navigation */
.table-row:focus {
  @apply outline-none ring-2 ring-table-primary ring-offset-2 ring-offset-table-bg;
}

.table-row:focus-visible {
  @apply outline-none ring-2 ring-table-primary ring-offset-2 ring-offset-table-bg;
}

/* Responsive design */
@media (max-width: 768px) {
  .table-row {
    @apply text-sm;
  }
}
</style>
