<template>
  <div
    class="table-pagination"
    :class="paginationClasses"
  >
    <!-- Total info -->
    <div
      v-if="showTotal"
      class="pagination-total"
    >
      <slot
        name="total"
        :total="total"
        :range="[startIndex + 1, endIndex]"
        :current="currentPage"
      >
        共 {{ total }} 条记录
        <template v-if="total > 0"> ，显示第 {{ startIndex + 1 }}-{{ endIndex }} 条 </template>
      </slot>
    </div>

    <!-- Page size selector -->
    <div
      v-if="showSizeChanger && pageSizeOptions.length > 1"
      class="pagination-size-changer"
    >
      <span class="size-changer-label">每页显示</span>
      <select
        :value="pageSize"
        class="size-changer-select"
        @change="handlePageSizeChange"
      >
        <option
          v-for="size in pageSizeOptions"
          :key="size"
          :value="size"
        >
          {{ size }} 条
        </option>
      </select>
    </div>

    <!-- Pagination controls -->
    <div
      v-if="totalPages > 1"
      class="pagination-controls"
    >
      <!-- First page button -->
      <button
        class="pagination-btn pagination-first"
        :disabled="!hasPrevPage"
        :title="'首页'"
        @click="handleFirstPage"
      >
        <slot name="first-icon">
          <Icon
            icon="first"
            class="pagination-icon"
            size="sm"
          />
        </slot>
      </button>

      <!-- Previous page button -->
      <button
        class="pagination-btn pagination-prev"
        :disabled="!hasPrevPage"
        :title="'上一页'"
        @click="handlePrevPage"
      >
        <slot name="prev-icon">
          <Icon
            icon="prev"
            class="pagination-icon"
            size="sm"
          />
        </slot>
      </button>

      <!-- Page numbers -->
      <div class="pagination-pages">
        <template
          v-for="(page, index) in pageRange"
          :key="`page-${index}`"
        >
          <!-- Ellipsis -->
          <span
            v-if="page === -1"
            class="pagination-ellipsis"
          >
            <slot name="ellipsis">...</slot>
          </span>
          <!-- Page number -->
          <button
            v-else
            class="pagination-btn pagination-page"
            :class="{
              'pagination-page-active': page === currentPage
            }"
            :title="`第 ${page} 页`"
            @click="handlePageClick(page)"
          >
            {{ page }}
          </button>
        </template>
      </div>

      <!-- Next page button -->
      <button
        class="pagination-btn pagination-next"
        :disabled="!hasNextPage"
        :title="'下一页'"
        @click="handleNextPage"
      >
        <slot name="next-icon">
          <Icon
            icon="next"
            class="pagination-icon"
            size="sm"
          />
        </slot>
      </button>

      <!-- Last page button -->
      <button
        class="pagination-btn pagination-last"
        :disabled="!hasNextPage"
        :title="'末页'"
        @click="handleLastPage"
      >
        <slot name="last-icon">
          <Icon
            icon="last"
            class="pagination-icon"
            size="sm"
          />
        </slot>
      </button>
    </div>

    <!-- Quick jumper -->
    <div
      v-if="showQuickJumper && totalPages > 1"
      class="pagination-jumper"
    >
      <span class="jumper-label">跳至</span>
      <input
        ref="jumperInput"
        v-model.number="jumperValue"
        type="number"
        class="jumper-input"
        :min="1"
        :max="totalPages"
        :placeholder="`1-${totalPages}`"
        @keydown.enter="handleJumperEnter"
        @blur="handleJumperBlur"
      />
      <span class="jumper-label">页</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Icon } from '../Icon'

// Props definition
interface Props {
  currentPage: number
  pageSize: number
  total: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
  startIndex: number
  endIndex: number
  pageRange: number[]
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
  pageSizeOptions?: number[]
  size?: 'small' | 'default' | 'large'
  simple?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: true,
  pageSizeOptions: () => [10, 20, 50, 100],
  size: 'default',
  simple: false,
  disabled: false
})

// Emits definition
interface Emits {
  pageChange: [page: number]
  pageSizeChange: [pageSize: number]
  showSizeChange: [current: number, size: number]
}

const emit = defineEmits<Emits>()

// Template refs
const jumperInput = ref<HTMLInputElement>()

// Internal state
const jumperValue = ref<number | string>('')

// Computed properties
const paginationClasses = computed(() => {
  const classes = ['pagination']

  if (props.size !== 'default') {
    classes.push(`pagination-${props.size}`)
  }

  if (props.simple) {
    classes.push('pagination-simple')
  }

  if (props.disabled) {
    classes.push('pagination-disabled')
  }

  return classes
})

// Event handlers
const handlePageClick = (page: number): void => {
  if (page !== props.currentPage && !props.disabled) {
    emit('pageChange', page)
  }
}

const handlePrevPage = (): void => {
  if (props.hasPrevPage && !props.disabled) {
    emit('pageChange', props.currentPage - 1)
  }
}

const handleNextPage = (): void => {
  if (props.hasNextPage && !props.disabled) {
    emit('pageChange', props.currentPage + 1)
  }
}

const handleFirstPage = (): void => {
  if (props.hasPrevPage && !props.disabled) {
    emit('pageChange', 1)
  }
}

const handleLastPage = (): void => {
  if (props.hasNextPage && !props.disabled) {
    emit('pageChange', props.totalPages)
  }
}

const handlePageSizeChange = (event: Event): void => {
  if (props.disabled) return

  const target = event.target as HTMLSelectElement
  const newSize = parseInt(target.value, 10)

  if (newSize !== props.pageSize) {
    emit('pageSizeChange', newSize)
    emit('showSizeChange', props.currentPage, newSize)
  }
}

const handleJumperEnter = (): void => {
  handleJumperSubmit()
}

const handleJumperBlur = (): void => {
  handleJumperSubmit()
}

const handleJumperSubmit = (): void => {
  if (props.disabled) return

  const value =
    typeof jumperValue.value === 'string' ? parseInt(jumperValue.value, 10) : jumperValue.value

  if (!isNaN(value) && value >= 1 && value <= props.totalPages && value !== props.currentPage) {
    emit('pageChange', value)
  }

  // Clear the input
  jumperValue.value = ''
}

// Watchers
watch(
  () => props.currentPage,
  () => {
    // Clear jumper input when page changes externally
    jumperValue.value = ''
  }
)

// Expose methods for parent components
defineExpose({
  focus: () => {
    jumperInput.value?.focus()
  },
  blur: () => {
    jumperInput.value?.blur()
  }
})
</script>

<style scoped>
@reference "../../styles/base.css";

.table-pagination {
  @apply flex items-center justify-between gap-4 p-4 bg-table-bg border-t border-table-border;
}

.pagination-small {
  @apply text-sm p-2;
}

.pagination-large {
  @apply text-lg p-6;
}

.pagination-disabled {
  @apply opacity-50 pointer-events-none;
}

/* Total info */
.pagination-total {
  @apply text-table-text-secondary text-sm;
}

/* Page size changer */
.pagination-size-changer {
  @apply flex items-center gap-2 text-sm;
}

.size-changer-label {
  @apply text-table-text-secondary;
}

.size-changer-select {
  @apply px-2 py-1 border border-table-border rounded bg-table-bg text-table-text;
  @apply focus:outline-none focus:ring-2 focus:ring-table-primary focus:border-table-primary;
  @apply hover:border-table-primary transition-colors;
}

/* Pagination controls */
.pagination-controls {
  @apply flex items-center gap-1;
}

.pagination-btn {
  @apply flex items-center justify-center min-w-8 h-8 px-2 border border-table-border rounded;
  @apply bg-table-bg text-table-text hover:bg-table-row-hover-bg hover:border-table-border-hover;
  @apply focus:outline-none focus:ring-2 focus:ring-table-primary focus:border-table-primary;
  @apply transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  @apply disabled:hover:bg-table-bg disabled:hover:border-table-border;
}

.pagination-btn:not(:disabled):hover {
  @apply text-table-primary;
}

.pagination-icon {
  @apply w-4 h-4;
}

/* Page numbers */
.pagination-pages {
  @apply flex items-center gap-1;
}

.pagination-page {
  @apply min-w-8 h-8;
}

.pagination-page-active {
  @apply bg-table-primary text-white border-table-primary;
  @apply hover:bg-table-primary hover:border-table-primary hover:text-white;
}

.pagination-ellipsis {
  @apply flex items-center justify-center min-w-8 h-8 text-table-text-secondary;
}

/* Quick jumper */
.pagination-jumper {
  @apply flex items-center gap-2 text-sm;
}

.jumper-label {
  @apply text-table-text-secondary;
}

.jumper-input {
  @apply w-16 px-2 py-1 border border-table-border rounded bg-table-bg text-table-text text-center;
  @apply focus:outline-none focus:ring-2 focus:ring-table-primary focus:border-table-primary;
  @apply hover:border-table-primary transition-colors;
}

.jumper-input::-webkit-outer-spin-button,
.jumper-input::-webkit-inner-spin-button {
  @apply appearance-none m-0;
}

.jumper-input[type='number'] {
  -moz-appearance: textfield;
}

/* Simple mode */
.pagination-simple .pagination-pages {
  @apply hidden;
}

.pagination-simple .pagination-total {
  @apply flex-1;
}

/* Responsive design */
@media (max-width: 768px) {
  .table-pagination {
    @apply flex-col gap-2 p-2;
  }

  .pagination-controls {
    @apply order-1;
  }

  .pagination-total {
    @apply order-2 text-center;
  }

  .pagination-size-changer,
  .pagination-jumper {
    @apply order-3 justify-center;
  }

  .pagination-btn {
    @apply min-w-6 h-6 text-sm;
  }

  .pagination-icon {
    @apply w-3 h-3;
  }
}

@media (max-width: 480px) {
  .pagination-size-changer,
  .pagination-jumper {
    @apply hidden;
  }

  .pagination-first,
  .pagination-last {
    @apply hidden;
  }
}
</style>
