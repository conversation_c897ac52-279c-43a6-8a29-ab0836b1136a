<template>
  <div
    ref="tableContainer"
    class="vue-table-container"
    :data-theme="currentTheme"
    :class="containerClasses"
    @keydown="handleKeydown"
  >
    <!-- Error boundary display -->
    <div
      v-if="error"
      class="table-error-boundary"
    >
      <div class="error-content">
        <h3 class="error-title">表格渲染错误</h3>
        <p class="error-message">{{ error.message }}</p>
        <button
          class="error-retry-btn"
          @click="handleRetry"
        >
          重试
        </button>
      </div>
    </div>

    <!-- Loading state -->
    <div
      v-else-if="isLoading"
      class="table-loading"
    >
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- Main table content -->
    <div
      v-else
      class="table-content"
    >
      <!-- Toolbar (placeholder for future implementation) -->
      <div
        v-if="toolbarConfig"
        class="table-toolbar-placeholder"
      >
        <div class="toolbar-title">{{ getToolbarTitle() }}</div>
      </div>

      <!-- Table wrapper -->
      <div
        class="table-wrapper"
        :style="tableWrapperStyle"
      >
        <!-- Table header -->
        <TableHeader
          :columns="processedColumns"
          :sort-config="sortConfigs"
          :resizable="true"
          :sticky="props.config.stickyHeader ?? false"
          @sort="handleSortChange"
          @resize="handleColumnResize"
          @headerClick="handleHeaderClick"
        >
          <!-- Pass through header slots -->
          <template
            v-for="column in processedColumns"
            :key="`header-${column.key}`"
            #[`header-${column.key}`]="slotProps"
          >
            <slot
              :name="`header-${column.key}`"
              v-bind="slotProps"
            />
          </template>
        </TableHeader>

        <!-- Table body -->
        <TableBody
          :data="displayData"
          :columns="processedColumns"
          :loading="false"
          :striped="props.config.striped"
          :hoverAble="props.config.hoverAble"
          :selected-rows="tableState.selectedRows"
          :editing-rows="new Set()"
          :disabled-rows="new Set()"
          :empty-text="'暂无数据'"
          :loading-text="'加载中...'"
          @rowClick="handleRowClick"
          @rowDblclick="handleRowDoubleClick"
          @rowMouseenter="handleRowMouseEnter"
          @rowMouseleave="handleRowMouseLeave"
          @rowContextmenu="handleRowContextMenu"
        >
          <!-- Pass through cell slots -->
          <template
            v-for="column in processedColumns"
            :key="`cell-${column.key}`"
            #[`cell-${column.key}`]="cellProps"
          >
            <slot
              :name="`cell-${column.key}`"
              v-bind="cellProps"
            />
          </template>
        </TableBody>
      </div>

      <!-- Pagination -->
      <TablePagination
        v-if="paginationConfig?.enabled"
        :current-page="paginationState.currentPage"
        :page-size="paginationState.pageSize"
        :total="paginationState.total"
        :total-pages="totalPages"
        :has-next-page="hasNextPage"
        :has-prev-page="hasPrevPage"
        :start-index="startIndex"
        :end-index="endIndex"
        :page-range="[...pageRange]"
        :show-size-changer="paginationConfig.showSizeChanger"
        :show-quick-jumper="paginationConfig.showQuickJumper"
        :show-total="paginationConfig.showTotal"
        :page-size-options="paginationConfig.pageSizeOptions"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        @show-size-change="handleShowSizeChange"
      >
        <!-- Pass through pagination slots -->
        <template #total="totalProps">
          <slot
            name="pagination-total"
            v-bind="totalProps"
          />
        </template>
        <template #first-icon>
          <slot name="pagination-first-icon" />
        </template>
        <template #prev-icon>
          <slot name="pagination-prev-icon" />
        </template>
        <template #next-icon>
          <slot name="pagination-next-icon" />
        </template>
        <template #last-icon>
          <slot name="pagination-last-icon" />
        </template>
        <template #ellipsis>
          <slot name="pagination-ellipsis" />
        </template>
      </TablePagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import TableHeader from './TableHeader.vue'
import TableBody from './TableBody.vue'
import TablePagination from './TablePagination.vue'
import { useSorting, useFiltering, usePagination } from '@/composables'
import type {
  TableConfig,
  TableColumn,
  TableRow,
  TableState,
  ToolbarConfig,
  PaginationConfig,
  PaginationState,
  ThemeConfig,
  SortConfig,
  FilterConfig
} from '@/types'

// Props definition
interface Props {
  config: TableConfig
}

const props = defineProps<Props>()

// Emits definition
interface Emits {
  rowClick: [row: TableRow, index: number]
  rowSelect: [selectedRows: TableRow[]]
  cellEdit: [value: unknown, row: TableRow, column: TableColumn]
  sortChange: [sortConfig: SortConfig[]]
  filterChange: [filterConfig: FilterConfig]
  pageChange: [pageConfig: PaginationState]
  error: [error: Error]
  loadingChange: [loading: boolean]
}

const emit = defineEmits<Emits>()

// Template refs
const tableContainer = ref<HTMLElement>()

// Internal state
const error = ref<Error | null>(null)
const loading = ref(false)

// Reactive column widths - this will store the current widths for each column
const columnWidths = ref<Record<string, number>>({})

// Initialize table state
const tableState = reactive<TableState>({
  originalData: [],
  processedData: [],
  filteredData: [],
  selectedRows: new Set(),
  editingCell: null,
  focusedCell: null,
  sortConfig: [],
  filterConfig: {},
  paginationState: {
    currentPage: 1,
    pageSize: 10,
    total: 0
  },
  virtualState: {
    scrollTop: 0,
    visibleStart: 0,
    visibleEnd: 0,
    totalHeight: 0,
    containerHeight: 0
  },
  currentTheme: 'default',
  loading: false,
  error: null
})

// Methods (defined early to avoid hoisting issues)
const handleError = (err: Error): void => {
  error.value = err
  tableState.error = err
  emit('error', err)
  console.error('Table error:', err)
}

const validateAndProcessColumns = (columns: TableColumn[]): TableColumn[] => {
  if (!Array.isArray(columns) || columns.length === 0) {
    throw new Error('表格列配置不能为空')
  }

  const processedColumns = columns.map((column, index) => {
    if (!column.key) {
      throw new Error(`第 ${index + 1} 列缺少必需的 key 属性`)
    }
    if (!column.title) {
      throw new Error(`列 "${column.key}" 缺少必需的 title 属性`)
    }

    return {
      align: 'left' as const,
      sortable: false,
      filterable: false,
      editable: false,
      ...column
    }
  })

  // 计算固定列的位置
  let leftPosition = 0
  let rightPosition = 0

  // 计算左固定列位置和z-index
  let leftZIndex = 15 // 左固定列从15开始递减
  for (let i = 0; i < processedColumns.length; i++) {
    const column = processedColumns[i]
    if (column.fixed === 'left') {
      column._fixedLeftPosition = leftPosition
      column._fixedZIndex = leftZIndex
      const width =
        typeof column.width === 'number'
          ? column.width
          : parseInt(column.width?.toString() || '100')
      leftPosition += width
      leftZIndex-- // 后面的列z-index更低
    }
  }

  // 计算右固定列位置和z-index（从右往左）
  let rightZIndex = 15 // 右固定列从15开始递减
  for (let i = processedColumns.length - 1; i >= 0; i--) {
    const column = processedColumns[i]
    if (column.fixed === 'right') {
      column._fixedRightPosition = rightPosition
      column._fixedZIndex = rightZIndex
      const width =
        typeof column.width === 'number'
          ? column.width
          : parseInt(column.width?.toString() || '100')
      rightPosition += width
      rightZIndex-- // 左边的列z-index更低
    }
  }

  // 标记边界固定列（用于显示阴影分隔）
  let lastLeftFixedIndex = -1
  let firstRightFixedIndex = -1

  // 找到最后一个左固定列
  for (let i = processedColumns.length - 1; i >= 0; i--) {
    if (processedColumns[i].fixed === 'left') {
      lastLeftFixedIndex = i
      break
    }
  }

  // 找到第一个右固定列
  for (let i = 0; i < processedColumns.length; i++) {
    if (processedColumns[i].fixed === 'right') {
      firstRightFixedIndex = i
      break
    }
  }

  // 标记边界列
  if (lastLeftFixedIndex >= 0) {
    processedColumns[lastLeftFixedIndex]._isLastLeftFixed = true
  }
  if (firstRightFixedIndex >= 0) {
    processedColumns[firstRightFixedIndex]._isFirstRightFixed = true
  }

  return processedColumns
}

const validateAndProcessData = (data: TableRow[]): TableRow[] => {
  if (!Array.isArray(data)) {
    throw new Error('表格数据必须是数组格式')
  }

  return data.map((row, index) => ({
    _id: row._id || index,
    _selected: false,
    _editing: false,
    _disabled: false,
    ...row
  }))
}

// Computed properties
const toolbarConfig = computed<ToolbarConfig | undefined>(() => props.config.toolbar)
const paginationConfig = computed<PaginationConfig | undefined>(() => props.config.pagination)
// const virtualConfig = computed<VirtualConfig | undefined>(() => props.config.virtual)
const themeConfig = computed<ThemeConfig | undefined>(() => props.config.theme)

const currentTheme = computed(() => themeConfig.value?.name || 'default')

// Computed loading state that combines internal loading and prop loading
const isLoading = computed(() => loading.value || props.config.loading || false)

const containerClasses = computed(() => {
  const classes = ['table-container']

  if (props.config.bordered) classes.push('table-bordered')
  if (props.config.striped) classes.push('table-striped')
  if (props.config.hoverAble) classes.push('table-hoverAble')
  if (props.config.size) classes.push(`table-size-${props.config.size}`)

  return classes
})

// 计算固定列位置的辅助函数
const calculateFixedPositions = (columns: TableColumn[]): void => {
  let leftPosition = 0
  let rightPosition = 0

  // 计算左固定列位置和z-index
  let leftZIndex = 15
  for (let i = 0; i < columns.length; i++) {
    const column = columns[i]
    if (column.fixed === 'left') {
      column._fixedLeftPosition = leftPosition
      column._fixedZIndex = leftZIndex
      const width =
        typeof column.width === 'number'
          ? column.width
          : parseInt(column.width?.toString() || '100')
      leftPosition += width
      leftZIndex--
    }
  }

  // 计算右固定列位置和z-index（从右往左）
  let rightZIndex = 15
  for (let i = columns.length - 1; i >= 0; i--) {
    const column = columns[i]
    if (column.fixed === 'right') {
      column._fixedRightPosition = rightPosition
      column._fixedZIndex = rightZIndex
      const width =
        typeof column.width === 'number'
          ? column.width
          : parseInt(column.width?.toString() || '100')
      rightPosition += width
      rightZIndex--
    }
  }
}

const processedColumns = computed<TableColumn[]>(() => {
  if (error.value) return []
  try {
    const columns = validateAndProcessColumns(props.config.columns)
    // Apply reactive column widths
    const columnsWithWidths = columns.map(column => ({
      ...column,
      width: columnWidths.value[column.key] ?? column.width
    }))

    // 重新计算固定列位置（考虑动态宽度变化）
    calculateFixedPositions(columnsWithWidths)

    return columnsWithWidths
  } catch (err) {
    handleError(err as Error)
    return []
  }
})

const processedData = computed<TableRow[]>(() => {
  if (error.value) return []
  try {
    return validateAndProcessData(props.config.data)
  } catch (err) {
    handleError(err as Error)
    return []
  }
})

// Initialize sorting functionality
const {
  sortConfigs,
  // isSorted,
  sortedData,
  handleSort,
  handleHeaderClick: handleSortHeaderClick,
  setSortConfigs,
  clearSort,
  getSortState
} = useSorting(processedData, processedColumns, {
  multiSort: props.config.multiSort ?? false,
  defaultSortConfigs: props.config.defaultSort || [],
  customSortFunctions: props.config.customSortFunctions || {},
  onSortChange: configs => {
    // Update table state
    tableState.sortConfig = configs
    emit('sortChange', configs)
  }
})

// Initialize filtering functionality
const {
  searchConfig,
  filterConditions,
  filteredData,
  isFiltered,
  isSearching,
  searchText,
  setSearchText,
  addFilterCondition,
  removeFilterCondition,
  clearAllFilters,
  clearSearch,
  getColumnValues,
  highlightText,
  createSimpleFilter
} = useFiltering(sortedData, processedColumns, {
  caseSensitive: props.config.caseSensitive ?? false,
  enableRegex: props.config.enableRegex ?? true,
  enableHighlight: props.config.enableHighlight ?? true,
  debounceMs: props.config.searchDebounce ?? 300,
  onFilterChange: config => {
    // Update table state
    tableState.filterConfig = config
    emit('filterChange', config)
  }
})

// Initialize pagination functionality
const {
  paginationState,
  currentPage,
  pageSize,
  total,
  totalPages,
  hasNextPage,
  hasPrevPage,
  startIndex,
  endIndex,
  pageRange,
  setPage,
  setPageSize,
  setTotal,
  nextPage,
  prevPage,
  firstPage,
  lastPage,
  jumpToPage,
  reset: resetPagination,
  updatePagination,
  getPageData,
  isValidPage,
  getPageInfo
} = usePagination(0, {
  defaultPageSize: paginationConfig.value?.pageSize || 10,
  defaultCurrentPage: paginationConfig.value?.currentPage || 1,
  showSizeChanger: paginationConfig.value?.showSizeChanger ?? true,
  showQuickJumper: paginationConfig.value?.showQuickJumper ?? true,
  showTotal: paginationConfig.value?.showTotal ?? true,
  pageSizeOptions: paginationConfig.value?.pageSizeOptions || [10, 20, 50, 100],
  onPageChange: (page, pageSize) => {
    // Update table state
    tableState.paginationState.currentPage = page
    tableState.paginationState.pageSize = pageSize
    emit('pageChange', { currentPage: page, pageSize, total: paginationState.total })
  },
  onPageSizeChange: (page, pageSize) => {
    // Update table state
    tableState.paginationState.currentPage = page
    tableState.paginationState.pageSize = pageSize
    emit('pageChange', { currentPage: page, pageSize, total: paginationState.total })
  }
})

const displayData = computed<TableRow[]>(() => {
  // Start with filtered data (which includes sorting)
  let data = filteredData.value

  // Apply pagination if enabled
  if (paginationConfig.value?.enabled) {
    data = getPageData(data)
  }

  return data
})

const tableWrapperStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.config.height) {
    style['height'] =
      typeof props.config.height === 'number' ? `${props.config.height}px` : props.config.height
  }

  if (props.config.maxHeight) {
    style['maxHeight'] =
      typeof props.config.maxHeight === 'number'
        ? `${props.config.maxHeight}px`
        : props.config.maxHeight
  }

  return style
})

// Duplicate methods removed

const getToolbarTitle = (): string => {
  if (!toolbarConfig.value?.title) return ''

  if (typeof toolbarConfig.value.title === 'string') {
    return toolbarConfig.value.title
  }

  return toolbarConfig.value.title.text || ''
}

const handleRetry = (): void => {
  error.value = null
  tableState.error = null
  // Re-trigger reactive updates
  nextTick(() => {
    initializeTableState()
  })
}

const handleSortChange = (column: TableColumn, direction: 'asc' | 'desc' | null): void => {
  handleSort(column, direction)
}

const handleColumnResize = (column: TableColumn, width: number): void => {
  // 更新列宽存储
  columnWidths.value[column.key] = width

  // 触发重新计算表格布局
  nextTick(() => {
    // 可以在这里添加回调通知父组件列宽变化
    // Column resized
  })
}

const handleHeaderClick = (column: TableColumn, event: MouseEvent): void => {
  // Handle sorting if column is sortable
  if (column.sortable) {
    handleSortHeaderClick(column, event)
  }

  // Header clicked
}

const handleRowClick = (row: TableRow, index: number, event: MouseEvent): void => {
  emit('rowClick', row, index)
  // Row clicked
}

const handleRowDoubleClick = (row: TableRow, index: number, event: MouseEvent): void => {
  // Row double clicked
}

const handleRowMouseEnter = (row: TableRow, index: number, event: MouseEvent): void => {
  // Row mouse enter
}

const handleRowMouseLeave = (row: TableRow, index: number, event: MouseEvent): void => {
  // Row mouse leave
}

const handleRowContextMenu = (row: TableRow, index: number, event: MouseEvent): void => {
  // Row context menu
}

const handleKeydown = (event: KeyboardEvent): void => {
  // Placeholder for keyboard navigation
  // Will be implemented in later tasks
  // Handle keyboard event
}

const handlePageChange = (page: number): void => {
  setPage(page)
}

const handlePageSizeChange = (pageSize: number): void => {
  setPageSize(pageSize)
}

const handleShowSizeChange = (current: number, size: number): void => {
  // This is called when both page and size change
  // Page size changed
}

const initializeTableState = (): void => {
  try {
    // Initialize data state
    tableState.originalData = [...processedData.value]
    tableState.processedData = [...processedData.value]
    tableState.filteredData = [...filteredData.value]

    // Initialize sort state
    tableState.sortConfig = [...sortConfigs.value]

    // Initialize filter state
    tableState.filterConfig = {
      search: searchConfig.value,
      conditions: filterConditions.value
    }

    // Initialize pagination state
    if (paginationConfig.value?.enabled) {
      const totalItems = filteredData.value.length
      setTotal(totalItems)

      // Update pagination config if provided
      updatePagination({
        currentPage: paginationConfig.value.currentPage || 1,
        pageSize: paginationConfig.value.pageSize || 10,
        total: totalItems
      })

      // Sync with table state
      tableState.paginationState = {
        currentPage: paginationState.currentPage,
        pageSize: paginationState.pageSize,
        total: paginationState.total
      }
    }

    // Initialize theme
    tableState.currentTheme = currentTheme.value

    // Clear error state
    error.value = null
    tableState.error = null
  } catch (err) {
    handleError(err as Error)
  }
}

const setLoading = (isLoading: boolean): void => {
  loading.value = isLoading
  tableState.loading = isLoading
  emit('loadingChange', isLoading)
}

// Lifecycle hooks
onMounted(() => {
  try {
    initializeTableState()
  } catch (err) {
    handleError(err as Error)
  }
})

onUnmounted(() => {
  // Cleanup
  tableState.selectedRows.clear()
})

// Watchers
watch(
  () => props.config.data,
  () => {
    try {
      initializeTableState()
    } catch (err) {
      handleError(err as Error)
    }
  },
  { deep: true }
)

watch(
  () => props.config.columns,
  () => {
    try {
      initializeTableState()
    } catch (err) {
      handleError(err as Error)
    }
  },
  { deep: true }
)

watch(
  () => props.config.loading,
  newLoading => {
    if (newLoading !== undefined) {
      setLoading(newLoading)
    }
  }
)

// Watch filtered data changes to update pagination total
watch(
  filteredData,
  newFilteredData => {
    if (paginationConfig.value?.enabled) {
      const totalItems = newFilteredData.length
      setTotal(totalItems)

      // Sync with table state
      tableState.paginationState.total = totalItems
      tableState.paginationState.currentPage = paginationState.currentPage
    }
  },
  { immediate: true }
)

// Watch search text changes to reset pagination
watch(searchText, (newSearchText, oldSearchText) => {
  if (
    paginationConfig.value?.enabled &&
    oldSearchText !== undefined &&
    newSearchText !== oldSearchText
  ) {
    setPage(1)
  }
})

// Expose public methods for parent components
defineExpose({
  getTableState: () => tableState,
  refresh: initializeTableState,
  setLoading,
  clearError: () => {
    error.value = null
    tableState.error = null
  },
  // Sorting methods
  setSortConfigs,
  clearSort,
  getSortState,
  // Filtering methods
  setSearchText,
  addFilterCondition,
  removeFilterCondition,
  clearAllFilters,
  clearSearch,
  getColumnValues,
  highlightText,
  createSimpleFilter,
  // Pagination methods
  setPage,
  setPageSize,
  setTotal,
  nextPage,
  prevPage,
  firstPage,
  lastPage,
  jumpToPage,
  resetPagination,
  updatePagination,
  getPageData,
  isValidPage,
  getPageInfo,
  // State getters
  getFilteredData: () => filteredData.value,
  getSearchText: () => searchText.value,
  isFiltered: () => isFiltered.value,
  isSearching: () => isSearching.value,
  getCurrentPage: () => currentPage.value,
  getPageSize: () => pageSize.value,
  getTotal: () => total.value,
  getTotalPages: () => totalPages.value
})
</script>

<style scoped>
@reference "../../styles/base.css";

.vue-table-container {
  @apply w-full relative;
}

.table-container {
  @apply bg-table-bg border border-table-border rounded-table overflow-hidden;
}

.table-bordered {
  @apply border-2;
}

.table-size-small {
  @apply text-sm;
}

.table-size-large {
  @apply text-lg;
}

/* Error boundary styles */
.table-error-boundary {
  @apply p-8 text-center bg-red-50 border border-red-200 rounded-table;
}

.error-content {
  @apply max-w-md mx-auto;
}

.error-title {
  @apply text-lg font-semibold text-red-800 mb-2;
}

.error-message {
  @apply text-red-600 mb-4;
}

.error-retry-btn {
  @apply px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors;
}

/* Loading styles */
.table-loading {
  @apply flex flex-col items-center justify-center p-8 text-table-text-secondary;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-table-border border-t-table-primary rounded-full animate-spin mb-2;
}

.loading-text {
  @apply text-sm;
}

/* Table content styles */
.table-content {
  @apply w-full;
}

.table-toolbar-placeholder {
  @apply p-4 border-b border-table-border bg-table-bg-secondary;
}

.toolbar-title {
  @apply font-semibold text-table-text;
}

.table-wrapper {
  @apply overflow-auto;
  /* 优化横向滚动体验 */
  scrollbar-width: thin;
  scrollbar-color: var(--table-border) transparent;
}

/* Webkit 浏览器的滚动条样式 */
.table-wrapper::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: var(--table-bg-secondary);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: var(--table-border);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--table-border-hover);
}

/* 横向滚动时的表格样式优化 */
.table-wrapper {
  /* 确保滚动时的性能 */
  will-change: scroll-position;
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

/* 修复滚动时可能出现的边框问题 */
.table-wrapper .table-header,
.table-wrapper .table-body {
  /* 确保表格内容不会因为滚动而出现样式问题 */
  min-width: fit-content;
}

/* 确保固定列在滚动时保持正确的层级和样式 */
.table-wrapper {
  position: relative;
}

/* 为固定列添加渐变边缘效果，增强视觉层次 */
.table-wrapper::before,
.table-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 10px;
  pointer-events: none;
  z-index: 15;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.table-wrapper::before {
  left: 0;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
}

.table-wrapper::after {
  right: 0;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.1), transparent);
}

/* 当表格可以滚动时显示渐变边缘 */
.table-wrapper.can-scroll-left::before {
  opacity: 1;
}

.table-wrapper.can-scroll-right::after {
  opacity: 1;
}

/* Table header integration */

/* Table body integration */

/* Search highlighting styles */
:deep(.table-search-highlight) {
  @apply bg-yellow-200 text-yellow-900 px-1 rounded;
}

:deep(.table-search-highlight.dark) {
  @apply bg-yellow-800 text-yellow-100;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-cell,
  .table-cell {
    @apply px-2 py-2 text-sm;
  }

  .table-toolbar-placeholder,
  .table-pagination-placeholder {
    @apply p-2;
  }
}
</style>
