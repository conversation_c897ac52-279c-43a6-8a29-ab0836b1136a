import { describe, it, expect, beforeEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import Table from './Table.vue'
import type { TableConfig, TableColumn, TableRow } from '@/types'

describe('Table Component', () => {
  let wrapper: VueWrapper<any>
  let mockConfig: TableConfig

  const mockColumns: TableColumn[] = [
    { key: 'id', title: 'ID', width: 80 },
    { key: 'name', title: '姓名', width: 120 },
    { key: 'age', title: '年龄', width: 80, align: 'center' },
    { key: 'email', title: '邮箱', width: 200 }
  ]

  const mockData: TableRow[] = [
    { id: 1, name: '张三', age: 25, email: '<EMAIL>' },
    { id: 2, name: '李四', age: 30, email: '<EMAIL>' },
    { id: 3, name: '王五', age: 28, email: '<EMAIL>' }
  ]

  beforeEach(() => {
    mockConfig = {
      columns: mockColumns,
      data: mockData,
      bordered: true,
      striped: true,
      hoverAble: true,
      size: 'medium'
    }
  })

  describe('Basic Rendering', () => {
    it('should render table container with correct classes', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const container = wrapper.find('.vue-table-container')
      expect(container.exists()).toBe(true)
      expect(container.classes()).toContain('table-bordered')
      expect(container.classes()).toContain('table-size-medium')
    })

    it('should render table headers correctly', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const headerCells = wrapper.findAll('.header-cell')
      expect(headerCells).toHaveLength(4)
      expect(headerCells[0].text()).toBe('ID')
      expect(headerCells[1].text()).toBe('姓名')
      expect(headerCells[2].text()).toBe('年龄')
      expect(headerCells[3].text()).toBe('邮箱')
    })

    it('should render table data correctly', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const rows = wrapper.findAll('.table-row')
      expect(rows).toHaveLength(3)

      const firstRowCells = rows[0].findAll('.table-cell')
      expect(firstRowCells[0].text()).toBe('1')
      expect(firstRowCells[1].text()).toBe('张三')
      expect(firstRowCells[2].text()).toBe('25')
      expect(firstRowCells[3].text()).toBe('<EMAIL>')
    })

    it('should apply column styles correctly', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const headerCells = wrapper.findAll('.header-cell')
      const firstCell = headerCells[0]
      const centerAlignedCell = headerCells[2]

      expect(firstCell.attributes('style')).toContain('width: 80px')
      expect(centerAlignedCell.attributes('style')).toContain('text-align: center')
    })
  })

  describe('Empty State', () => {
    it('should show empty state when no data provided', () => {
      const emptyConfig = {
        ...mockConfig,
        data: []
      }

      wrapper = mount(Table, {
        props: { config: emptyConfig }
      })

      const emptyState = wrapper.find('.table-empty')
      expect(emptyState.exists()).toBe(true)
      expect(emptyState.find('.empty-text').text()).toBe('暂无数据')
    })
  })

  describe('Loading State', () => {
    it('should show loading state when loading prop is true', async () => {
      const loadingConfig = {
        ...mockConfig,
        loading: true
      }

      wrapper = mount(Table, {
        props: { config: loadingConfig }
      })

      await wrapper.vm.$nextTick()

      const loadingState = wrapper.find('.table-loading')
      expect(loadingState.exists()).toBe(true)
      expect(loadingState.find('.loading-text').text()).toBe('加载中...')
      expect(loadingState.find('.loading-spinner').exists()).toBe(true)
    })

    it('should emit loading-change event when loading state changes', async () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const vm = wrapper.vm as any
      vm.setLoading(true)

      await wrapper.vm.$nextTick()
      expect(wrapper.emitted('loadingChange')).toBeTruthy()
      expect(wrapper.emitted('loadingChange')![0]).toEqual([true])
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid columns configuration', async () => {
      const invalidConfig = {
        ...mockConfig,
        columns: []
      }

      wrapper = mount(Table, {
        props: { config: invalidConfig }
      })

      await wrapper.vm.$nextTick()

      const errorBoundary = wrapper.find('.table-error-boundary')
      expect(errorBoundary.exists()).toBe(true)
      expect(errorBoundary.find('.error-title').text()).toBe('表格渲染错误')
    })

    it('should handle missing column key', async () => {
      const invalidConfig = {
        ...mockConfig,
        columns: [{ title: 'Test' } as any]
      }

      wrapper = mount(Table, {
        props: { config: invalidConfig }
      })

      await wrapper.vm.$nextTick()

      const errorBoundary = wrapper.find('.table-error-boundary')
      expect(errorBoundary.exists()).toBe(true)
    })

    it('should handle retry functionality', async () => {
      const invalidConfig = {
        ...mockConfig,
        columns: []
      }

      wrapper = mount(Table, {
        props: { config: invalidConfig }
      })

      await wrapper.vm.$nextTick()

      const retryButton = wrapper.find('.error-retry-btn')
      expect(retryButton.exists()).toBe(true)

      await retryButton.trigger('click')
      expect(wrapper.emitted('error')).toBeTruthy()
    })

    it('should emit error event when error occurs', async () => {
      const invalidConfig = {
        ...mockConfig,
        columns: []
      }

      wrapper = mount(Table, {
        props: { config: invalidConfig }
      })

      await wrapper.vm.$nextTick()

      expect(wrapper.emitted('error')).toBeTruthy()
      expect(wrapper.emitted('error')![0][0]).toBeInstanceOf(Error)
    })
  })

  describe('Configuration Validation', () => {
    it('should validate and process columns correctly', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const vm = wrapper.vm as any
      const processedColumns = vm.processedColumns

      expect(processedColumns).toHaveLength(4)
      expect(processedColumns[0]).toMatchObject({
        key: 'id',
        title: 'ID',
        align: 'left',
        sortable: false,
        filterable: false,
        editable: false
      })
    })

    it('should validate and process data correctly', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const vm = wrapper.vm as any
      const processedData = vm.processedData

      expect(processedData).toHaveLength(3)
      expect(processedData[0]).toMatchObject({
        id: 1,
        name: '张三',
        _id: 0, // Index-based ID when no _id provided
        _selected: false,
        _editing: false,
        _disabled: false
      })
    })

    it('should handle invalid data format', async () => {
      const invalidConfig = {
        ...mockConfig,
        data: 'invalid' as any
      }

      wrapper = mount(Table, {
        props: { config: invalidConfig }
      })

      await wrapper.vm.$nextTick()

      const errorBoundary = wrapper.find('.table-error-boundary')
      expect(errorBoundary.exists()).toBe(true)
    })
  })

  describe('Pagination', () => {
    it('should show pagination info when pagination is enabled', async () => {
      const paginationConfig = {
        ...mockConfig,
        pagination: {
          enabled: true,
          pageSize: 2,
          currentPage: 1,
          total: 3,
          showTotal: true
        }
      }

      wrapper = mount(Table, {
        props: { config: paginationConfig }
      })

      await wrapper.vm.$nextTick()

      const paginationInfo = wrapper.find('.pagination-total')
      expect(paginationInfo.exists()).toBe(true)
      expect(paginationInfo.text()).toContain('共 3 条记录')
    })

    it('should display correct page data when pagination is enabled', async () => {
      const paginationConfig = {
        ...mockConfig,
        pagination: {
          enabled: true,
          pageSize: 2,
          currentPage: 1,
          total: 3
        }
      }

      wrapper = mount(Table, {
        props: { config: paginationConfig }
      })

      await wrapper.vm.$nextTick()

      const rows = wrapper.findAll('.table-row')
      expect(rows).toHaveLength(2) // Only 2 rows per page
    })
  })

  describe('Toolbar', () => {
    it('should show toolbar when toolbar config is provided', () => {
      const toolbarConfig = {
        ...mockConfig,
        toolbar: {
          title: 'Test Table'
        }
      }

      wrapper = mount(Table, {
        props: { config: toolbarConfig }
      })

      const toolbar = wrapper.find('.table-toolbar-placeholder')
      expect(toolbar.exists()).toBe(true)
      expect(toolbar.find('.toolbar-title').text()).toBe('Test Table')
    })

    it('should handle complex toolbar title configuration', () => {
      const toolbarConfig = {
        ...mockConfig,
        toolbar: {
          title: {
            text: 'Complex Title',
            subtitle: 'Subtitle'
          }
        }
      }

      wrapper = mount(Table, {
        props: { config: toolbarConfig }
      })

      const toolbar = wrapper.find('.table-toolbar-placeholder')
      expect(toolbar.exists()).toBe(true)
      expect(toolbar.find('.toolbar-title').text()).toBe('Complex Title')
    })
  })

  describe('Responsive Design', () => {
    it('should apply responsive classes correctly', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const container = wrapper.find('.vue-table-container')
      expect(container.classes()).toContain('table-container')
    })
  })

  describe('Theme Support', () => {
    it('should apply theme data attribute', () => {
      const themedConfig = {
        ...mockConfig,
        theme: {
          name: 'dark' as const
        }
      }

      wrapper = mount(Table, {
        props: { config: themedConfig }
      })

      const container = wrapper.find('.vue-table-container')
      expect(container.attributes('data-theme')).toBe('dark')
    })

    it('should default to default theme when no theme specified', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const container = wrapper.find('.vue-table-container')
      expect(container.attributes('data-theme')).toBe('default')
    })
  })

  describe('Exposed Methods', () => {
    it('should expose getTableState method', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const vm = wrapper.vm as any
      const tableState = vm.getTableState()
      expect(tableState).toBeDefined()
      expect(tableState.originalData).toHaveLength(3)
    })

    it('should expose refresh method', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const vm = wrapper.vm as any
      expect(typeof vm.refresh).toBe('function')
    })

    it('should expose setLoading method', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const vm = wrapper.vm as any
      expect(typeof vm.setLoading).toBe('function')
    })

    it('should expose clearError method', () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const vm = wrapper.vm as any
      expect(typeof vm.clearError).toBe('function')
    })
  })

  describe('Watchers', () => {
    it('should reinitialize when data changes', async () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const newData = [{ id: 4, name: '赵六', age: 35, email: '<EMAIL>' }]

      await wrapper.setProps({
        config: {
          ...mockConfig,
          data: newData
        }
      })

      const rows = wrapper.findAll('.table-row')
      expect(rows).toHaveLength(1)
      expect(rows[0].findAll('.table-cell')[1].text()).toBe('赵六')
    })

    it('should reinitialize when columns change', async () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const newColumns = [
        { key: 'id', title: 'New ID' },
        { key: 'name', title: 'New Name' }
      ]

      await wrapper.setProps({
        config: {
          ...mockConfig,
          columns: newColumns
        }
      })

      const headerCells = wrapper.findAll('.header-cell')
      expect(headerCells).toHaveLength(2)
      expect(headerCells[0].text()).toBe('New ID')
      expect(headerCells[1].text()).toBe('New Name')
    })
  })

  describe('Keyboard Events', () => {
    it('should handle keydown events', async () => {
      wrapper = mount(Table, {
        props: { config: mockConfig }
      })

      const container = wrapper.find('.vue-table-container')
      await container.trigger('keydown', { key: 'ArrowDown' })

      // Verify that the keydown event is handled (no specific emission expected currently)
      expect(container.exists()).toBe(true)
    })
  })
})
