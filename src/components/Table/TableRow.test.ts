import { describe, it, expect, beforeEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import TableRow from './TableRow.vue'
import type { TableColumn, TableRow as TableRowType } from '@/types'

describe('TableRow Component', () => {
  let wrapper: VueWrapper<any>
  let mockColumns: TableColumn[]
  let mockRow: TableRowType

  beforeEach(() => {
    mockColumns = [
      { key: 'id', title: 'ID', width: 80 },
      { key: 'name', title: '姓名', width: 120 },
      { key: 'age', title: '年龄', width: 80, align: 'center' },
      { key: 'email', title: '邮箱', width: 200, editable: true }
    ]

    mockRow = {
      id: 1,
      name: '张三',
      age: 25,
      email: '<EMAIL>'
    }
  })

  describe('Basic Rendering', () => {
    it('should render table row with cells', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const row = wrapper.find('.table-row')
      expect(row.exists()).toBe(true)

      const cells = wrapper.findAll('.table-cell')
      expect(cells).toHaveLength(4)
    })

    it('should apply row classes correctly', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0,
          selected: true,
          disabled: false,
          editing: true,
          striped: true,
          hoverAble: true
        }
      })

      const row = wrapper.find('.table-row')
      expect(row.classes()).toContain('row-selected')
      expect(row.classes()).toContain('row-editing')
      expect(row.classes()).toContain('row-striped')
      expect(row.classes()).toContain('row-hoverAble')
      expect(row.classes()).not.toContain('row-disabled')
    })

    it('should apply disabled class and not hoverAble when disabled', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0,
          disabled: true,
          hoverAble: true
        }
      })

      const row = wrapper.find('.table-row')
      expect(row.classes()).toContain('row-disabled')
      expect(row.classes()).not.toContain('row-hoverAble')
    })
  })

  describe('Row Height', () => {
    it('should apply custom row height as number', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0,
          rowHeight: 60
        }
      })

      const row = wrapper.find('.table-row')
      expect(row.attributes('style')).toContain('height: 60px')
      expect(row.attributes('style')).toContain('min-height: 60px')
    })

    it('should apply custom row height as string', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0,
          rowHeight: '4rem'
        }
      })

      const row = wrapper.find('.table-row')
      expect(row.attributes('style')).toContain('height: 4rem')
      expect(row.attributes('style')).toContain('min-height: 4rem')
    })
  })

  describe('Cell Value Handling', () => {
    it('should get cell values correctly', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const vm = wrapper.vm as any
      expect(vm.getCellValue(mockColumns[0])).toBe(1)
      expect(vm.getCellValue(mockColumns[1])).toBe('张三')
      expect(vm.getCellValue(mockColumns[2])).toBe(25)
      expect(vm.getCellValue(mockColumns[3])).toBe('<EMAIL>')
    })

    it('should format display values correctly', () => {
      const rowWithDifferentTypes = {
        id: 1,
        name: '张三',
        active: true,
        data: { key: 'value' },
        empty: null,
        undefined
      }

      const columnsWithDifferentTypes = [
        { key: 'id', title: 'ID' },
        { key: 'name', title: 'Name' },
        { key: 'active', title: 'Active' },
        { key: 'data', title: 'Data' },
        { key: 'empty', title: 'Empty' },
        { key: 'undefined', title: 'Undefined' }
      ]

      wrapper = mount(TableRow, {
        props: {
          row: rowWithDifferentTypes,
          columns: columnsWithDifferentTypes,
          index: 0
        }
      })

      const vm = wrapper.vm as any
      expect(vm.getCellDisplayValue(columnsWithDifferentTypes[0])).toBe('1')
      expect(vm.getCellDisplayValue(columnsWithDifferentTypes[1])).toBe('张三')
      expect(vm.getCellDisplayValue(columnsWithDifferentTypes[2])).toBe('是')
      expect(vm.getCellDisplayValue(columnsWithDifferentTypes[3])).toBe('{"key":"value"}')
      expect(vm.getCellDisplayValue(columnsWithDifferentTypes[4])).toBe('')
      expect(vm.getCellDisplayValue(columnsWithDifferentTypes[5])).toBe('')
    })

    it('should handle boolean false values correctly', () => {
      const rowWithFalse = { active: false }
      const columns = [{ key: 'active', title: 'Active' }]

      wrapper = mount(TableRow, {
        props: {
          row: rowWithFalse,
          columns,
          index: 0
        }
      })

      const vm = wrapper.vm as any
      expect(vm.getCellDisplayValue(columns[0])).toBe('否')
    })
  })

  describe('Editable Columns', () => {
    it('should identify editable columns correctly', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const vm = wrapper.vm as any
      expect(vm.isColumnEditable(mockColumns[0])).toBe(false)
      expect(vm.isColumnEditable(mockColumns[1])).toBe(false)
      expect(vm.isColumnEditable(mockColumns[2])).toBe(false)
      expect(vm.isColumnEditable(mockColumns[3])).toBe(true) // email column is editable
    })
  })

  describe('Row Events', () => {
    it('should emit click event when row is clicked', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const row = wrapper.find('.table-row')
      await row.trigger('click')

      expect(wrapper.emitted('click')).toBeTruthy()
      expect(wrapper.emitted('click')![0][0]).toBeInstanceOf(MouseEvent)
    })

    it('should not emit click event when row is disabled', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0,
          disabled: true
        }
      })

      const row = wrapper.find('.table-row')
      await row.trigger('click')

      expect(wrapper.emitted('click')).toBeFalsy()
    })

    it('should emit dblclick event when row is double clicked', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const row = wrapper.find('.table-row')
      await row.trigger('dblclick')

      expect(wrapper.emitted('dblclick')).toBeTruthy()
      expect(wrapper.emitted('dblclick')![0][0]).toBeInstanceOf(MouseEvent)
    })

    it('should not emit dblclick event when row is disabled', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0,
          disabled: true
        }
      })

      const row = wrapper.find('.table-row')
      await row.trigger('dblclick')

      expect(wrapper.emitted('dblclick')).toBeFalsy()
    })

    it('should emit mouseenter event when mouse enters row', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const row = wrapper.find('.table-row')
      await row.trigger('mouseenter')

      expect(wrapper.emitted('mouseenter')).toBeTruthy()
      expect(wrapper.emitted('mouseenter')![0][0]).toBeInstanceOf(MouseEvent)
    })

    it('should emit mouseleave event when mouse leaves row', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const row = wrapper.find('.table-row')
      await row.trigger('mouseleave')

      expect(wrapper.emitted('mouseleave')).toBeTruthy()
      expect(wrapper.emitted('mouseleave')![0][0]).toBeInstanceOf(MouseEvent)
    })

    it('should emit contextmenu event when row is right clicked', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const row = wrapper.find('.table-row')
      await row.trigger('contextmenu')

      expect(wrapper.emitted('contextmenu')).toBeTruthy()
      expect(wrapper.emitted('contextmenu')![0][0]).toBeInstanceOf(MouseEvent)
    })

    it('should not emit contextmenu event when row is disabled', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0,
          disabled: true
        }
      })

      const row = wrapper.find('.table-row')
      await row.trigger('contextmenu')

      expect(wrapper.emitted('contextmenu')).toBeFalsy()
    })
  })

  describe('Cell Events', () => {
    it('should emit cell-click event when cell is clicked', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const firstCell = wrapper.findAll('.table-cell')[0]
      await firstCell.trigger('click')

      expect(wrapper.emitted('cell-click')).toBeTruthy()
      expect(wrapper.emitted('cell-click')![0][0]).toEqual(mockColumns[0])
      expect(wrapper.emitted('cell-click')![0][1]).toBe(1) // cell value
    })

    it('should emit cell-dblclick event when cell is double clicked', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const firstCell = wrapper.findAll('.table-cell')[0]
      await firstCell.trigger('dblclick')

      expect(wrapper.emitted('cell-dblclick')).toBeTruthy()
      expect(wrapper.emitted('cell-dblclick')![0][0]).toEqual(mockColumns[0])
      expect(wrapper.emitted('cell-dblclick')![0][1]).toBe(1) // cell value
    })

    it('should emit cell-edit event when editable cell is double clicked', async () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const emailCell = wrapper.findAll('.table-cell')[3] // email column is editable
      await emailCell.trigger('dblclick')

      expect(wrapper.emitted('cell-edit')).toBeTruthy()
      expect(wrapper.emitted('cell-edit')![0][0]).toEqual(mockColumns[3])
      expect(wrapper.emitted('cell-edit')![0][1]).toBe('<EMAIL>')
    })

    it('should emit cell-change event when cell value changes', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const vm = wrapper.vm as any
      vm.handleCellChange(mockColumns[3], '<EMAIL>', '<EMAIL>')

      expect(wrapper.emitted('cell-change')).toBeTruthy()
      expect(wrapper.emitted('cell-change')?.[0]).toEqual([
        mockColumns[3],
        '<EMAIL>',
        '<EMAIL>'
      ])
    })
  })

  describe('Slots', () => {
    it('should render custom cell slot content', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        },
        slots: {
          'cell-name': '<div class="custom-name-cell">Custom Name</div>'
        }
      })

      const customCell = wrapper.find('.custom-name-cell')
      expect(customCell.exists()).toBe(true)
      expect(customCell.text()).toBe('Custom Name')
    })

    it('should pass correct props to cell slots', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        },
        slots: {
          'cell-name': `
            <template #default="{ value, row, column, index }">
              <div class="slot-test">
                Value: {{ value }}, 
                Row ID: {{ row.id }}, 
                Column: {{ column.key }}, 
                Index: {{ index }}
              </div>
            </template>
          `
        }
      })

      const slotContent = wrapper.find('.slot-test')
      expect(slotContent.exists()).toBe(true)
    })
  })

  describe('Default Cell Content', () => {
    it('should render default cell content when no slot provided', () => {
      wrapper = mount(TableRow, {
        props: {
          row: mockRow,
          columns: mockColumns,
          index: 0
        }
      })

      const cells = wrapper.findAll('.table-cell')

      // Check that cells contain the expected text content
      expect(cells[0].text()).toContain('1')
      expect(cells[1].text()).toContain('张三')
      expect(cells[2].text()).toContain('25')
      expect(cells[3].text()).toContain('<EMAIL>')
    })
  })
})
