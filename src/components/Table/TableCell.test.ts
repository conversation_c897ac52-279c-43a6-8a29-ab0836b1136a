import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import TableCell from './TableCell.vue'
import type { TableColumn, TableRow } from '@/types'

describe('TableCell Component', () => {
  let wrapper: VueWrapper<any>
  let mockColumn: TableColumn
  let mockRow: TableRow

  beforeEach(() => {
    mockColumn = {
      key: 'name',
      title: '姓名',
      width: 120,
      align: 'left'
    }

    mockRow = {
      id: 1,
      name: '张三',
      age: 25,
      email: '<EMAIL>'
    }
  })

  describe('Basic Rendering', () => {
    it('should render table cell with correct structure', () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: '张三',
          index: 0
        }
      })

      const cell = wrapper.find('.table-cell')
      expect(cell.exists()).toBe(true)

      const cellContent = wrapper.find('.cell-content')
      expect(cellContent.exists()).toBe(true)

      const cellText = wrapper.find('.cell-text')
      expect(cellText.exists()).toBe(true)
      expect(cellText.text()).toBe('张三')
    })

    it('should apply cell classes correctly', () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, align: 'center', editable: true },
          row: mockRow,
          value: '张三',
          index: 0,
          selected: true,
          disabled: false,
          editing: true
        }
      })

      const cell = wrapper.find('.table-cell')
      expect(cell.classes()).toContain('cell-align-center')
      expect(cell.classes()).toContain('cell-editable')
      expect(cell.classes()).toContain('cell-selected')
      expect(cell.classes()).toContain('cell-editing')
      expect(cell.classes()).not.toContain('cell-disabled')
    })

    it('should apply disabled class when disabled', () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: '张三',
          index: 0,
          disabled: true
        }
      })

      const cell = wrapper.find('.table-cell')
      expect(cell.classes()).toContain('cell-disabled')
    })
  })

  describe('Cell Styles', () => {
    it('should apply column width styles correctly', () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, width: 150, minWidth: 100, maxWidth: 200 },
          row: mockRow,
          value: '张三',
          index: 0
        }
      })

      const cell = wrapper.find('.table-cell')
      const style = cell.attributes('style')
      expect(style).toContain('width: 150px')
      expect(style).toContain('min-width: 100px') // Should use the explicit minWidth
      expect(style).toContain('max-width: 200px') // Should use the explicit maxWidth
    })

    it('should apply min and max width constraints', () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, minWidth: 100, maxWidth: 200 },
          row: mockRow,
          value: '张三',
          index: 0
        }
      })

      const cell = wrapper.find('.table-cell')
      const style = cell.attributes('style')
      expect(style).toContain('min-width: 100px')
      expect(style).toContain('max-width: 200px')
    })

    it('should apply text alignment styles', () => {
      const alignments: Array<'left' | 'center' | 'right'> = ['left', 'center', 'right']

      alignments.forEach(align => {
        wrapper = mount(TableCell, {
          props: {
            column: { ...mockColumn, align },
            row: mockRow,
            value: '张三',
            index: 0
          }
        })

        const cell = wrapper.find('.table-cell')
        expect(cell.attributes('style')).toContain(`text-align: ${align}`)
      })
    })

    it('should apply fixed column styles', () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, fixed: 'left' },
          row: mockRow,
          value: '张三',
          index: 0
        }
      })

      const cell = wrapper.find('.table-cell')
      expect(cell.classes()).toContain('cell-fixed-left')
      expect(cell.attributes('style')).toContain('position: sticky')
      expect(cell.attributes('style')).toContain('left: 0px')
    })
  })

  describe('Display Values', () => {
    it('should display string values correctly', () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: 'Hello World',
          index: 0
        }
      })

      const cellText = wrapper.find('.cell-text')
      expect(cellText.text()).toBe('Hello World')
    })

    it('should display number values correctly', () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: 42,
          index: 0
        }
      })

      const cellText = wrapper.find('.cell-text')
      expect(cellText.text()).toBe('42')
    })

    it('should display boolean true as "是"', () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: true,
          index: 0
        }
      })

      const cellText = wrapper.find('.cell-text')
      expect(cellText.text()).toBe('是')
    })

    it('should display boolean false as "否"', () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: false,
          index: 0
        }
      })

      const cellText = wrapper.find('.cell-text')
      expect(cellText.text()).toBe('否')
    })

    it('should display object values as JSON string', () => {
      const objectValue = { key: 'value', number: 123 }

      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: objectValue,
          index: 0
        }
      })

      const cellText = wrapper.find('.cell-text')
      expect(cellText.text()).toBe('{"key":"value","number":123}')
    })

    it('should display null and undefined as empty string', () => {
      // Test null
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: null,
          index: 0
        }
      })

      let cellText = wrapper.find('.cell-text')
      expect(cellText.text()).toBe('')

      // Test undefined
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: undefined,
          index: 0
        }
      })

      cellText = wrapper.find('.cell-text')
      expect(cellText.text()).toBe('')
    })
  })

  describe('Custom Render Function', () => {
    it('should use custom render function when provided', () => {
      const customRender = vi.fn(() => 'Custom Rendered Content')

      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, render: customRender },
          row: mockRow,
          value: '张三',
          index: 0
        }
      })

      // The render function is called by Vue's h() function with props and context
      expect(customRender).toHaveBeenCalled()
    })
  })

  describe('Cell Events', () => {
    it('should emit cell-click event when cell is clicked', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: '张三',
          index: 0
        }
      })

      const cell = wrapper.find('.table-cell')
      await cell.trigger('click')

      expect(wrapper.emitted('cell-click')).toBeTruthy()
      expect(wrapper.emitted('cell-click')![0]).toEqual([mockColumn, '张三', expect.any(Object)])
    })

    it('should not emit cell-click event when disabled', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: '张三',
          index: 0,
          disabled: true
        }
      })

      const cell = wrapper.find('.table-cell')
      await cell.trigger('click')

      expect(wrapper.emitted('cell-click')).toBeFalsy()
    })

    it('should emit cell-dblclick event when cell is double clicked', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: '张三',
          index: 0
        }
      })

      const cell = wrapper.find('.table-cell')
      await cell.trigger('dblclick')

      expect(wrapper.emitted('cell-dblclick')).toBeTruthy()
      expect(wrapper.emitted('cell-dblclick')![0]).toEqual([mockColumn, '张三', expect.any(Object)])
    })

    it('should emit cell-edit event when editable cell is double clicked', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: '张三',
          index: 0
        }
      })

      const cell = wrapper.find('.table-cell')
      await cell.trigger('dblclick')

      expect(wrapper.emitted('cell-edit')).toBeTruthy()
      expect(wrapper.emitted('cell-edit')![0]).toEqual([{ ...mockColumn, editable: true }, '张三'])
    })

    it('should not emit events when disabled', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: '张三',
          index: 0,
          disabled: true
        }
      })

      const cell = wrapper.find('.table-cell')
      await cell.trigger('click')
      await cell.trigger('dblclick')

      expect(wrapper.emitted('cell-click')).toBeFalsy()
      expect(wrapper.emitted('cell-dblclick')).toBeFalsy()
      expect(wrapper.emitted('cell-edit')).toBeFalsy()
    })
  })

  describe('Editing Mode', () => {
    it('should show input editor for string values', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: '张三',
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const editor = wrapper.find('.cell-editor')
      expect(editor.exists()).toBe(true)

      const input = wrapper.find('.cell-input')
      expect(input.exists()).toBe(true)
      expect((input.element as HTMLInputElement).value).toBe('张三')
    })

    it('should show number input for number values', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: 25,
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const input = wrapper.find('.cell-input')
      expect(input.exists()).toBe(true)
      expect(input.attributes('type')).toBe('number')
      expect((input.element as HTMLInputElement).value).toBe('25')
    })

    it('should show textarea for long string values', async () => {
      const longText =
        'This is a very long text that should trigger textarea editor instead of input'

      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: longText,
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const textarea = wrapper.find('.cell-textarea')
      expect(textarea.exists()).toBe(true)
      expect((textarea.element as HTMLTextAreaElement).value).toBe(longText)
    })

    it('should show select for boolean values', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: true,
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const select = wrapper.find('.cell-select')
      expect(select.exists()).toBe(true)

      const options = wrapper.findAll('option')
      expect(options).toHaveLength(2)
      expect(options[0].text()).toBe('是')
      expect(options[1].text()).toBe('否')
    })

    it('should detect email input type', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: '<EMAIL>',
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const input = wrapper.find('.cell-input')
      expect(input.attributes('type')).toBe('email')
    })

    it('should detect URL input type', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: 'https://example.com',
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const input = wrapper.find('.cell-input')
      expect(input.attributes('type')).toBe('url')
    })

    it('should focus editor when entering editing mode', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: '张三',
          index: 0,
          editing: false
        },
        attachTo: document.body // Attach to DOM for focus testing
      })

      // Change to editing mode
      await wrapper.setProps({ editing: true })
      await nextTick()

      const input = wrapper.find('.cell-input')
      expect(document.activeElement).toBe(input.element)

      wrapper.unmount()
    })
  })

  describe('Editor Events', () => {
    it('should emit cell-change event when editor value changes on blur', async () => {
      const editableColumn = { ...mockColumn, editable: true }

      wrapper = mount(TableCell, {
        props: {
          column: editableColumn,
          row: mockRow,
          value: '张三',
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const input = wrapper.find('.cell-input')
      await input.setValue('李四')
      await input.trigger('blur')

      expect(wrapper.emitted('cell-change')).toBeTruthy()
      expect(wrapper.emitted('cell-change')![0]).toEqual([editableColumn, '张三', '李四'])
    })

    it('should not emit cell-change event when value is unchanged', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: '张三',
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const input = wrapper.find('.cell-input')
      await input.trigger('blur')

      expect(wrapper.emitted('cell-change')).toBeFalsy()
    })

    it('should handle Enter key to confirm edit', async () => {
      const editableColumn = { ...mockColumn, editable: true }

      wrapper = mount(TableCell, {
        props: {
          column: editableColumn,
          row: mockRow,
          value: '张三',
          index: 0,
          editing: true
        },
        attachTo: document.body
      })

      await nextTick()

      const input = wrapper.find('.cell-input')
      await input.setValue('李四')
      await input.trigger('keydown', { key: 'Enter' })

      // The Enter key should trigger blur, which emits the change
      await input.trigger('blur')

      expect(wrapper.emitted('cell-change')).toBeTruthy()
      expect(wrapper.emitted('cell-change')![0]).toEqual([editableColumn, '张三', '李四'])

      wrapper.unmount()
    })

    it('should handle Escape key to cancel edit', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: '张三',
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const input = wrapper.find('.cell-input')
      await input.setValue('李四')
      await input.trigger('keydown', { key: 'Escape' })

      // Value should be reset to original
      expect((input.element as HTMLInputElement).value).toBe('张三')
    })

    it('should not handle Enter with Shift key in textarea', async () => {
      const longText =
        'This is a very long text that should trigger textarea editor instead of input'

      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: longText,
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const textarea = wrapper.find('.cell-textarea')
      await textarea.setValue('New long text')
      await textarea.trigger('keydown', { key: 'Enter', shiftKey: true })

      // Should not emit cell-change because Shift+Enter should add new line
      expect(wrapper.emitted('cell-change')).toBeFalsy()
    })
  })

  describe('Value Watchers', () => {
    it('should update edit value when prop value changes', async () => {
      wrapper = mount(TableCell, {
        props: {
          column: { ...mockColumn, editable: true },
          row: mockRow,
          value: '张三',
          index: 0,
          editing: true
        }
      })

      await nextTick()

      const input = wrapper.find('.cell-input')
      expect((input.element as HTMLInputElement).value).toBe('张三')

      // Change prop value
      await wrapper.setProps({ value: '李四' })
      await nextTick()

      expect((input.element as HTMLInputElement).value).toBe('李四')
    })
  })

  describe('Slots', () => {
    it('should render default slot content', () => {
      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: '张三',
          index: 0
        },
        slots: {
          default: '<div class="custom-content">Custom Cell Content</div>'
        }
      })

      const customContent = wrapper.find('.custom-content')
      expect(customContent.exists()).toBe(true)
      expect(customContent.text()).toBe('Custom Cell Content')
    })

    it('should pass correct props to default slot', () => {
      const slotContent = vi.fn()

      wrapper = mount(TableCell, {
        props: {
          column: mockColumn,
          row: mockRow,
          value: '张三',
          index: 0
        },
        slots: {
          default: slotContent
        }
      })

      expect(slotContent).toHaveBeenCalledWith({
        value: '张三',
        row: mockRow,
        column: mockColumn,
        index: 0
      })
    })
  })
})
