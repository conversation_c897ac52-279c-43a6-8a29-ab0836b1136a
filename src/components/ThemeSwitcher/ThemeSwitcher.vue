<template>
  <div class="theme-switcher">
    <label
      v-if="showLabel"
      class="theme-switcher-label"
    >
      {{ label }}
    </label>

    <div class="theme-switcher-controls">
      <!-- Theme selector -->
      <select
        v-model="selectedTheme"
        class="theme-selector"
        @change="handleThemeChange"
      >
        <option
          v-for="preset in availableThemes"
          :key="preset.name"
          :value="preset.name"
        >
          {{ preset.displayName }}
        </option>
      </select>

      <!-- Quick dark mode toggle -->
      <button
        v-if="showDarkToggle"
        type="button"
        class="dark-mode-toggle"
        :class="{ active: isDarkTheme }"
        @click="toggleDarkMode"
        :title="isDarkTheme ? 'Switch to light mode' : 'Switch to dark mode'"
      >
        <span class="dark-mode-icon">
          <Icon
            :icon="isDarkTheme ? 'light' : 'dark'"
            size="sm"
          />
        </span>
      </button>

      <!-- Transitions toggle -->
      <button
        v-if="showTransitionsToggle"
        type="button"
        class="transitions-toggle"
        :class="{ active: transitionsEnabled }"
        @click="toggleTransitions"
        :title="transitionsEnabled ? 'Disable transitions' : 'Enable transitions'"
      >
        <span class="transitions-icon">
          <Icon
            :icon="transitionsEnabled ? 'lucide:zap' : 'lucide:pause'"
            size="sm"
          />
        </span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Icon } from '../Icon'
import { useTheme } from '../../composables/useTheme'
import { themePresets } from '../../styles/themes'
import type { ThemeName } from '../../types/theme'

// Props
interface Props {
  label?: string
  showLabel?: boolean
  showDarkToggle?: boolean
  showTransitionsToggle?: boolean
  size?: 'sm' | 'md' | 'lg'
}

withDefaults(defineProps<Props>(), {
  label: 'Theme',
  showLabel: true,
  showDarkToggle: true,
  showTransitionsToggle: true,
  size: 'md'
})

// Emits
interface Emits {
  (e: 'theme-change', theme: string): void
  (e: 'dark-mode-toggle', isDark: boolean): void
  (e: 'transitions-toggle', enabled: boolean): void
}

const emit = defineEmits<Emits>()

// Use theme composable
const {
  currentTheme,
  isDarkTheme,
  transitionsEnabled,
  setTheme,
  toggleDarkMode: toggleDark,
  setTransitions
} = useTheme()

// Local state
const selectedTheme = ref<string>(currentTheme.value)

// Available themes
const availableThemes = computed(() => themePresets)

// Watch for external theme changes
watch(currentTheme, newTheme => {
  selectedTheme.value = newTheme
})

// Handle theme change
const handleThemeChange = () => {
  setTheme(selectedTheme.value as ThemeName)
  emit('theme-change', selectedTheme.value)
}

// Toggle dark mode
const toggleDarkMode = () => {
  toggleDark()
  emit('dark-mode-toggle', isDarkTheme.value)
}

// Toggle transitions
const toggleTransitions = () => {
  setTransitions(!transitionsEnabled.value)
  emit('transitions-toggle', transitionsEnabled.value)
}
</script>

<style scoped>
.theme-switcher {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.theme-switcher-label {
  font-size: var(--table-font-size-sm);
  font-weight: var(--table-font-weight-medium);
  color: var(--table-text-secondary);
}

.theme-switcher-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.theme-selector {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--table-border);
  border-radius: var(--table-border-radius);
  background-color: var(--table-bg);
  color: var(--table-text);
  font-size: var(--table-font-size);
  transition: var(--table-transition);
  min-width: 120px;
}

.theme-selector:hover {
  border-color: var(--table-primary);
}

.theme-selector:focus {
  outline: none;
  border-color: var(--table-primary);
  box-shadow: var(--table-shadow-focus);
}

.dark-mode-toggle,
.transitions-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--table-border);
  border-radius: var(--table-border-radius);
  background-color: var(--table-bg);
  color: var(--table-text);
  cursor: pointer;
  transition: var(--table-transition);
}

.dark-mode-toggle:hover,
.transitions-toggle:hover {
  border-color: var(--table-primary);
  background-color: var(--table-bg-secondary);
}

.dark-mode-toggle.active,
.transitions-toggle.active {
  background-color: var(--table-primary);
  border-color: var(--table-primary);
  color: white;
}

.dark-mode-icon,
.transitions-icon {
  font-size: 1rem;
  line-height: 1;
}

/* Size variations */
.theme-switcher[data-size='sm'] .theme-selector {
  padding: 0.25rem 0.5rem;
  font-size: var(--table-font-size-sm);
  min-width: 100px;
}

.theme-switcher[data-size='sm'] .dark-mode-toggle,
.theme-switcher[data-size='sm'] .transitions-toggle {
  width: 2rem;
  height: 2rem;
}

.theme-switcher[data-size='lg'] .theme-selector {
  padding: 0.75rem 1rem;
  font-size: var(--table-font-size-lg);
  min-width: 140px;
}

.theme-switcher[data-size='lg'] .dark-mode-toggle,
.theme-switcher[data-size='lg'] .transitions-toggle {
  width: 3rem;
  height: 3rem;
}
</style>
