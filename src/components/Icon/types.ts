/**
 * 图标组件属性类型定义
 */
/**
 * 图标尺寸类型
 */
export type IconSize = 'xs' | 'sm' | 'default' | 'md' | 'lg' | 'xl' | '2xl' | number

/**
 * 图标组件属性类型定义
 */
export interface IconProps {
  /** 图标名称，可以是语义化名称或者 iconify 格式的图标名 */
  icon: string

  /** 图标尺寸 */
  size?: IconSize

  /** 图标颜色，可以是 Tailwind 颜色类名或 'current' */
  color?: string

  /** 自定义 CSS 类 */
  class?: string
}

/**
 * 内置图标名称映射类型
 */
export type SemanticIconNames =
  // 排序图标
  | 'sort-asc'
  | 'sort-desc'

  // 分页图标
  | 'first'
  | 'prev'
  | 'next'
  | 'last'

  // 通用图标
  | 'empty'
  | 'expand'
  | 'collapse'

  // 主题切换图标
  | 'light'
  | 'dark'
  | 'auto'

  // 工具栏图标
  | 'create'
  | 'edit'
  | 'delete'
  | 'bulk-delete'
  | 'search'
  | 'filter'
  | 'refresh'
  | 'settings'
  | 'export'
  | 'fullscreen'
  | 'fullscreen-exit'
  | 'more'

/**
 * 图标名称类型，支持语义化名称和 iconify 格式名称
 */
export type IconName = SemanticIconNames | string
