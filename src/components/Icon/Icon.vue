<template>
  <Icon
    :icon="iconName"
    :class="classes"
    :style="styles"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import type { IconProps } from './types'

defineOptions({
  name: 'AppIcon',
  inheritAttrs: false
})

const props = withDefaults(defineProps<IconProps>(), {
  size: 'default',
  color: 'current'
})

// 图标名称映射，将语义化名称转换为实际的 iconify 名称
const iconMapping: Record<string, string> = {
  // 排序图标
  'sort-asc': 'lucide:arrow-up',
  'sort-desc': 'lucide:arrow-down',
  'sort-both': 'lucide:arrow-down-up',

  // 分页图标
  first: 'lucide:chevrons-left',
  prev: 'lucide:chevron-left',
  next: 'lucide:chevron-right',
  last: 'lucide:chevrons-right',

  // 通用图标
  empty: 'lucide:inbox',
  expand: 'lucide:chevron-right',
  collapse: 'lucide:chevron-down',

  // 主题切换图标
  light: 'lucide:sun',
  dark: 'lucide:moon',
  auto: 'lucide:monitor',

  // 工具栏图标
  create: 'lucide:plus',
  edit: 'lucide:edit-3',
  delete: 'lucide:trash-2',
  'bulk-delete': 'lucide:trash-2',
  search: 'lucide:search',
  filter: 'lucide:filter',
  refresh: 'lucide:refresh-cw',
  settings: 'lucide:settings',
  export: 'lucide:download',
  fullscreen: 'lucide:maximize',
  'fullscreen-exit': 'lucide:minimize',
  more: 'lucide:more-horizontal'
}

// 尺寸映射
const sizeMapping: Record<string, string> = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  default: 'w-5 h-5',
  md: 'w-6 h-6',
  lg: 'w-7 h-7',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10'
}

const iconName = computed(() => {
  // 如果直接提供了 iconify 图标名称（包含 : 的格式），直接使用
  if (props.icon.includes(':')) {
    return props.icon
  }

  // 否则通过映射表查找
  return iconMapping[props.icon] || props.icon
})

const classes = computed(() => {
  const classList: string[] = []

  // 添加尺寸类
  if (typeof props.size === 'string') {
    classList.push(sizeMapping[props.size] || sizeMapping.default)
  }

  // 添加颜色类
  if (props.color && props.color !== 'current') {
    classList.push(`text-${props.color}`)
  }

  // 添加额外的类
  if (props.class) {
    classList.push(props.class)
  }

  return classList.join(' ')
})

const styles = computed(() => {
  const styleObj: Record<string, string> = {}

  // 如果 size 是数字，使用自定义尺寸
  if (typeof props.size === 'number') {
    styleObj.width = `${props.size}px`
    styleObj.height = `${props.size}px`
  }

  return styleObj
})
</script>

<style scoped>
/* 图标基础样式 */
.icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}

/* 确保图标在不同上下文中的显示一致性 */
.icon svg {
  display: block;
  width: 100%;
  height: 100%;
}
</style>
