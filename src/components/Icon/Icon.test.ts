import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import Icon from './Icon.vue'

describe('Icon 组件', () => {
  it('应该正确渲染语义化图标名称', () => {
    const wrapper = mount(Icon, {
      props: {
        icon: 'sort-asc'
      }
    })

    // 应该渲染为 lucide:chevron-up
    expect(wrapper.find('[data-icon="lucide:chevron-up"]')).toBeTruthy()
  })

  it('应该正确渲染 iconify 格式图标', () => {
    const wrapper = mount(Icon, {
      props: {
        icon: 'mdi:home'
      }
    })

    // 应该直接使用 mdi:home
    expect(wrapper.find('[data-icon="mdi:home"]')).toBeTruthy()
  })

  it('应该应用正确的尺寸类', () => {
    const wrapper = mount(Icon, {
      props: {
        icon: 'search',
        size: 'lg'
      }
    })

    // 应该包含 w-7 h-7 类
    expect(wrapper.classes()).toContain('w-7')
    expect(wrapper.classes()).toContain('h-7')
  })

  it('应该支持数字尺寸', () => {
    const wrapper = mount(Icon, {
      props: {
        icon: 'search',
        size: 24
      }
    })

    // 应该设置内联样式
    expect(wrapper.element.style.width).toBe('24px')
    expect(wrapper.element.style.height).toBe('24px')
  })

  it('应该应用颜色类', () => {
    const wrapper = mount(Icon, {
      props: {
        icon: 'search',
        color: 'blue-500'
      }
    })

    expect(wrapper.classes()).toContain('text-blue-500')
  })

  it('应该支持自定义类', () => {
    const wrapper = mount(Icon, {
      props: {
        icon: 'search',
        class: 'custom-class'
      }
    })

    expect(wrapper.classes()).toContain('custom-class')
  })

  it('应该正确映射分页图标', () => {
    const testCases = [
      { semantic: 'first', expected: 'lucide:chevrons-left' },
      { semantic: 'prev', expected: 'lucide:chevron-left' },
      { semantic: 'next', expected: 'lucide:chevron-right' },
      { semantic: 'last', expected: 'lucide:chevrons-right' }
    ]

    testCases.forEach(({ semantic, expected }) => {
      const wrapper = mount(Icon, {
        props: {
          icon: semantic
        }
      })

      expect(wrapper.find(`[data-icon="${expected}"]`)).toBeTruthy()
    })
  })

  it('应该正确映射排序图标', () => {
    const testCases = [
      { semantic: 'sort-asc', expected: 'lucide:chevron-up' },
      { semantic: 'sort-desc', expected: 'lucide:chevron-down' }
    ]

    testCases.forEach(({ semantic, expected }) => {
      const wrapper = mount(Icon, {
        props: {
          icon: semantic
        }
      })

      expect(wrapper.find(`[data-icon="${expected}"]`)).toBeTruthy()
    })
  })

  it('应该正确映射主题图标', () => {
    const testCases = [
      { semantic: 'light', expected: 'lucide:sun' },
      { semantic: 'dark', expected: 'lucide:moon' },
      { semantic: 'auto', expected: 'lucide:monitor' }
    ]

    testCases.forEach(({ semantic, expected }) => {
      const wrapper = mount(Icon, {
        props: {
          icon: semantic
        }
      })

      expect(wrapper.find(`[data-icon="${expected}"]`)).toBeTruthy()
    })
  })

  it('应该处理未知的语义化图标名称', () => {
    const wrapper = mount(Icon, {
      props: {
        icon: 'unknown-icon'
      }
    })

    // 未知图标应该直接使用原始名称
    expect(wrapper.find('[data-icon="unknown-icon"]')).toBeTruthy()
  })
})