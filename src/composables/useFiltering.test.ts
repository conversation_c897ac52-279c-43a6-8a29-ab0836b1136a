// Unit tests for useFiltering composable
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ref, nextTick, effectScope } from 'vue'
import { useFiltering } from './useFiltering'
import type { TableRow, TableColumn } from '@/types'

// Test data
const testColumns = ref<TableColumn[]>([
  { key: 'name', title: 'Name', filterable: true },
  { key: 'age', title: 'Age', filterable: true },
  { key: 'email', title: 'Email', filterable: true },
  { key: 'status', title: 'Status', filterable: true },
  { key: 'score', title: 'Score', filterable: true },
  { key: 'internal', title: 'Internal', filterable: false }
])

const testData = ref<TableRow[]>([
  {
    _id: 1,
    name: '<PERSON>',
    age: 30,
    email: '<EMAIL>',
    status: 'active',
    score: 85.5
  },
  {
    _id: 2,
    name: '<PERSON>',
    age: 25,
    email: '<EMAIL>',
    status: 'inactive',
    score: 92.0
  },
  {
    _id: 3,
    name: '<PERSON>',
    age: 35,
    email: '<EMAIL>',
    status: 'active',
    score: 78.3
  },
  {
    _id: 4,
    name: '<PERSON>',
    age: null,
    email: '<EMAIL>',
    status: 'pending',
    score: null
  }
])

describe('useFiltering', () => {
  let filtering: ReturnType<typeof useFiltering>
  let scope: any

  // Helper function to create filtering with scope
  const createFiltering = (...args: Parameters<typeof useFiltering>) => {
    const localScope = effectScope()
    const result = localScope.run(() => useFiltering(...args))!
    return { result, scope: localScope }
  }

  beforeEach(() => {
    scope = effectScope()
    filtering = scope.run(() => useFiltering(testData, testColumns))!
  })

  afterEach(() => {
    if (scope) {
      scope.stop()
    }
  })

  describe('initialization', () => {
    it('should initialize with default values', () => {
      expect(filtering.searchText.value).toBe('')
      expect(filtering.filterConditions.value).toEqual([])
      expect(filtering.filterGroups.value).toEqual([])
      expect(filtering.isFiltered.value).toBe(false)
      expect(filtering.isSearching.value).toBe(false)
    })

    it('should initialize with default search config', () => {
      const customFiltering = useFiltering(testData, testColumns, {
        defaultSearch: {
          text: 'test',
          caseSensitive: true,
          highlight: false
        }
      })

      expect(customFiltering.searchConfig.value.text).toBe('test')
      expect(customFiltering.searchConfig.value.caseSensitive).toBe(true)
      expect(customFiltering.searchConfig.value.highlight).toBe(false)
    })

    it('should initialize with default filters', () => {
      const defaultFilters = [{ column: 'status', operator: 'equals' as const, value: 'active' }]

      const { result: customFiltering, scope: customScope } = createFiltering(testData, testColumns, {
        defaultFilters
      })

      // Cleanup after test
      customScope.stop()

      expect(customFiltering.filterConditions.value).toEqual(defaultFilters)
      expect(customFiltering.isFiltered.value).toBe(true)
    })
  })

  describe('filterable columns', () => {
    it('should return only filterable columns', () => {
      expect(filtering.filterableColumns.value).toHaveLength(5)
      expect(filtering.filterableColumns.value.every(col => col.filterable !== false)).toBe(true)
    })
  })

  describe('search functionality', () => {
    it('should filter data based on search text', async () => {
      // Use setSearchConfig to bypass debouncing
      filtering.setSearchConfig({
        text: 'john',
        caseSensitive: false,
        highlight: false
      })
      await nextTick()

      expect(filtering.isSearching.value).toBe(true)
      expect(filtering.filteredData.value).toHaveLength(2) // John Doe and Bob Johnson
    })

    it('should clear search', async () => {
      filtering.setSearchText('john')
      await nextTick()

      filtering.clearSearch()
      await nextTick()

      expect(filtering.searchText.value).toBe('')
      expect(filtering.isSearching.value).toBe(false)
      expect(filtering.filteredData.value).toHaveLength(4) // All data
    })

    it('should update search config', async () => {
      const newConfig = {
        text: 'test',
        caseSensitive: true,
        regex: true,
        highlight: false
      }

      filtering.setSearchConfig(newConfig)
      await nextTick()

      expect(filtering.searchConfig.value).toEqual(newConfig)
    })

    it('should debounce search text updates', async () => {
      const onSearchChange = vi.fn()
      const debouncedFiltering = useFiltering(testData, testColumns, {
        debounceMs: 50,
        onSearchChange
      })

      debouncedFiltering.setSearchText('test1')
      debouncedFiltering.setSearchText('test2')
      debouncedFiltering.setSearchText('test3')

      // Should not call immediately
      expect(onSearchChange).not.toHaveBeenCalled()

      // Wait for debounce
      await new Promise(resolve => setTimeout(resolve, 100))

      // Should call at least once with final value (may be called more due to reactive updates)
      expect(onSearchChange).toHaveBeenCalled()
      expect(onSearchChange).toHaveBeenLastCalledWith(expect.objectContaining({ text: 'test3' }))
    })
  })

  describe('filter conditions', () => {
    it('should add valid filter condition', () => {
      const condition = filtering.createSimpleFilter('status', 'equals', 'active')
      filtering.addFilterCondition(condition)

      expect(filtering.filterConditions.value).toHaveLength(1)
      expect(filtering.isFiltered.value).toBe(true)
      expect(filtering.filteredData.value).toHaveLength(2) // John and Bob are active
    })

    it('should reject invalid filter condition', () => {
      const invalidCondition = filtering.createSimpleFilter('nonexistent', 'equals', 'test')
      filtering.addFilterCondition(invalidCondition)

      expect(filtering.filterConditions.value).toHaveLength(0)
      expect(filtering.isFiltered.value).toBe(false)
    })

    it('should remove filter condition', () => {
      const condition = filtering.createSimpleFilter('status', 'equals', 'active')
      filtering.addFilterCondition(condition)

      expect(filtering.filterConditions.value).toHaveLength(1)

      filtering.removeFilterCondition(0)

      expect(filtering.filterConditions.value).toHaveLength(0)
      expect(filtering.isFiltered.value).toBe(false)
    })

    it('should update filter condition', () => {
      const condition = filtering.createSimpleFilter('status', 'equals', 'active')
      filtering.addFilterCondition(condition)

      const updatedCondition = filtering.createSimpleFilter('status', 'equals', 'inactive')
      filtering.updateFilterCondition(0, updatedCondition)

      expect(filtering.filterConditions.value[0].value).toBe('inactive')
      expect(filtering.filteredData.value).toHaveLength(1) // Only Jane is inactive
    })

    it('should clear all filters', () => {
      filtering.addFilterCondition(filtering.createSimpleFilter('status', 'equals', 'active'))
      filtering.addFilterCondition(filtering.createSimpleFilter('age', 'greaterThan', 25))

      expect(filtering.filterConditions.value).toHaveLength(2)

      filtering.clearAllFilters()

      expect(filtering.filterConditions.value).toHaveLength(0)
      expect(filtering.filterGroups.value).toHaveLength(0)
      expect(filtering.isFiltered.value).toBe(false)
    })
  })

  describe('filter groups', () => {
    it('should add filter group', () => {
      const conditions = [
        filtering.createSimpleFilter('status', 'equals', 'active'),
        filtering.createSimpleFilter('age', 'greaterThan', 25)
      ]
      const group = filtering.createAdvancedFilter(conditions, 'and')

      filtering.addFilterGroup(group)

      expect(filtering.filterGroups.value).toHaveLength(1)
      expect(filtering.isFiltered.value).toBe(true)
    })

    it('should remove filter group', () => {
      const conditions = [filtering.createSimpleFilter('status', 'equals', 'active')]
      const group = filtering.createAdvancedFilter(conditions)

      filtering.addFilterGroup(group)
      expect(filtering.filterGroups.value).toHaveLength(1)

      filtering.removeFilterGroup(0)
      expect(filtering.filterGroups.value).toHaveLength(0)
    })

    it('should validate filter groups', () => {
      const validConditions = [filtering.createSimpleFilter('status', 'equals', 'active')]
      const invalidConditions = [filtering.createSimpleFilter('nonexistent', 'equals', 'test')]

      const validGroup = filtering.createAdvancedFilter(validConditions)
      const invalidGroup = filtering.createAdvancedFilter(invalidConditions)

      expect(filtering.validateGroup(validGroup)).toBe(true)
      expect(filtering.validateGroup(invalidGroup)).toBe(false)
    })
  })

  describe('combined filtering', () => {
    it('should combine search and filter conditions', async () => {
      // Add filter for active status
      filtering.addFilterCondition(filtering.createSimpleFilter('status', 'equals', 'active'))

      // Add search for 'john' using setSearchConfig to bypass debouncing
      filtering.setSearchConfig({
        text: 'john',
        caseSensitive: false,
        highlight: false
      })
      await nextTick()

      // Should find John Doe and Bob Johnson (both active and contain 'john')
      expect(filtering.filteredData.value).toHaveLength(2)
      const names = filtering.filteredData.value.map(row => row.name)
      expect(names).toContain('John Doe')
      expect(names).toContain('Bob Johnson')
    })

    it('should apply multiple filter conditions with AND logic', () => {
      filtering.addFilterCondition(filtering.createSimpleFilter('status', 'equals', 'active'))
      filtering.addFilterCondition(filtering.createSimpleFilter('age', 'greaterThan', 32))

      // Should find only Bob (active and age > 32)
      expect(filtering.filteredData.value).toHaveLength(1)
      expect(filtering.filteredData.value[0].name).toBe('Bob Johnson')
    })
  })

  describe('column values', () => {
    it('should get unique column values', () => {
      const statusValues = filtering.getColumnValues('status')
      expect(statusValues).toEqual(['active', 'inactive', 'pending'])
    })

    it('should respect limit parameter', () => {
      const limitedValues = filtering.getColumnValues('status', 2)
      expect(limitedValues).toHaveLength(2)
    })
  })

  describe('text highlighting', () => {
    it('should highlight search matches', async () => {
      filtering.setSearchConfig({
        text: 'doe',
        caseSensitive: false,
        highlight: true
      })
      await nextTick()

      // Check that we have filtered data
      expect(filtering.filteredData.value.length).toBeGreaterThan(0)

      // The highlight info should be populated after filtering
      expect(filtering.highlightInfo.value.size).toBeGreaterThan(0)
    })

    it('should return original text when no highlights', () => {
      const text = 'No matches here'
      const result = filtering.highlightText(text, 'name', '1')
      expect(result).toBe(text)
    })
  })

  describe('validation', () => {
    it('should validate filter conditions', () => {
      const validCondition = filtering.createSimpleFilter('status', 'equals', 'active')
      const invalidCondition = filtering.createSimpleFilter('nonexistent', 'equals', 'test')

      expect(filtering.validateFilter(validCondition)).toBe(true)
      expect(filtering.validateFilter(invalidCondition)).toBe(false)
    })
  })

  describe('callbacks', () => {
    it('should call onFilterChange when filters change', () => {
      const onFilterChange = vi.fn()
      const callbackFiltering = useFiltering(testData, testColumns, {
        onFilterChange
      })

      const condition = callbackFiltering.createSimpleFilter('status', 'equals', 'active')
      callbackFiltering.addFilterCondition(condition)

      expect(onFilterChange).toHaveBeenCalledWith(
        expect.objectContaining({
          conditions: [condition]
        })
      )
    })

    it('should call onSearchChange when search changes', async () => {
      const onSearchChange = vi.fn()
      const callbackFiltering = useFiltering(testData, testColumns, {
        onSearchChange,
        debounceMs: 0 // Disable debounce for testing
      })

      const newConfig = {
        text: 'test',
        caseSensitive: true,
        highlight: false
      }

      callbackFiltering.setSearchConfig(newConfig)
      await nextTick()

      expect(onSearchChange).toHaveBeenCalledWith(newConfig)
    })
  })

  describe('reactive updates', () => {
    it('should update filtered data when source data changes', async () => {
      filtering.setSearchConfig({
        text: 'john',
        caseSensitive: false,
        highlight: false
      })
      await nextTick()

      expect(filtering.filteredData.value).toHaveLength(2)

      // Add new data with 'john'
      testData.value.push({
        _id: 5,
        name: 'Johnny Cash',
        age: 40,
        email: '<EMAIL>',
        status: 'active',
        score: 95
      })

      await nextTick()
      expect(filtering.filteredData.value).toHaveLength(3)
    })

    it('should validate filters when columns change', async () => {
      const condition = filtering.createSimpleFilter('status', 'equals', 'active')
      filtering.addFilterCondition(condition)

      expect(filtering.filterConditions.value).toHaveLength(1)

      // Remove the status column
      testColumns.value = testColumns.value.filter(col => col.key !== 'status')
      await nextTick()

      // Filter should be removed as column is no longer available
      expect(filtering.filterConditions.value).toHaveLength(0)
    })
  })

  describe('error handling', () => {
    it('should handle errors gracefully in filtering', () => {
      // Create invalid data that might cause errors
      const invalidData = ref([{ invalid: 'data' }])
      const errorFiltering = useFiltering(invalidData, testColumns)

      // Should not throw errors
      expect(() => {
        errorFiltering.setSearchText('test')
        errorFiltering.addFilterCondition(
          errorFiltering.createSimpleFilter('name', 'contains', 'test')
        )
      }).not.toThrow()
    })
  })
})
