import { computed, reactive, readonly, watch } from 'vue'
import type { Ref } from 'vue'
import type { PaginationConfig, PaginationState } from '@/types'

export interface UsePaginationOptions {
  defaultPageSize?: number
  defaultCurrentPage?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
  pageSizeOptions?: number[]
  onPageChange?: (page: number, pageSize: number) => void
  onPageSizeChange?: (current: number, size: number) => void
}

export interface UsePaginationReturn {
  // State
  paginationState: PaginationState
  currentPage: Readonly<Ref<number>>
  pageSize: Readonly<Ref<number>>
  total: Readonly<Ref<number>>

  // Computed
  totalPages: Readonly<Ref<number>>
  hasNextPage: Readonly<Ref<boolean>>
  hasPrevPage: Readonly<Ref<boolean>>
  startIndex: Readonly<Ref<number>>
  endIndex: Readonly<Ref<number>>
  pageRange: Readonly<Ref<readonly number[]>>

  // Methods
  setPage: (page: number) => void
  setPageSize: (size: number) => void
  setTotal: (total: number) => void
  nextPage: () => void
  prevPage: () => void
  firstPage: () => void
  lastPage: () => void
  jumpToPage: (page: number) => void
  reset: () => void
  updatePagination: (config: Partial<PaginationConfig>) => void

  // Utilities
  getPageData: <T>(data: T[]) => T[]
  isValidPage: (page: number) => boolean
  getPageInfo: () => {
    current: number
    pageSize: number
    total: number
    totalPages: number
    startIndex: number
    endIndex: number
  }
}

export function usePagination(
  initialTotal = 0,
  options: UsePaginationOptions = {}
): UsePaginationReturn {
  const {
    defaultPageSize = 10,
    defaultCurrentPage = 1,
    onPageChange,
    onPageSizeChange
  } = options

  // Reactive state
  const paginationState = reactive<PaginationState>({
    currentPage: defaultCurrentPage,
    pageSize: defaultPageSize,
    total: initialTotal
  })

  // Computed properties
  const currentPage = computed(() => paginationState.currentPage)
  const pageSize = computed(() => paginationState.pageSize)
  const total = computed(() => paginationState.total)

  const totalPages = computed(() => {
    return Math.ceil(paginationState.total / paginationState.pageSize) || 1
  })

  const hasNextPage = computed(() => {
    return paginationState.currentPage < totalPages.value
  })

  const hasPrevPage = computed(() => {
    return paginationState.currentPage > 1
  })

  const startIndex = computed(() => {
    return (paginationState.currentPage - 1) * paginationState.pageSize
  })

  const endIndex = computed(() => {
    const end = startIndex.value + paginationState.pageSize
    return Math.min(end, paginationState.total)
  })

  // Generate page range for pagination display
  const pageRange = computed(() => {
    const current = paginationState.currentPage
    const total = totalPages.value
    const range: number[] = []

    if (total <= 7) {
      // Show all pages if total is small
      for (let i = 1; i <= total; i++) {
        range.push(i)
      }
    } else {
      // Show smart pagination with ellipsis
      if (current <= 4) {
        // Show first 5 pages + ellipsis + last page
        for (let i = 1; i <= 5; i++) {
          range.push(i)
        }
        if (total > 6) {
          range.push(-1) // Ellipsis marker
          range.push(total)
        }
      } else if (current >= total - 3) {
        // Show first page + ellipsis + last 5 pages
        range.push(1)
        if (total > 6) {
          range.push(-1) // Ellipsis marker
        }
        for (let i = total - 4; i <= total; i++) {
          range.push(i)
        }
      } else {
        // Show first page + ellipsis + current-1, current, current+1 + ellipsis + last page
        range.push(1)
        range.push(-1) // Ellipsis marker
        for (let i = current - 1; i <= current + 1; i++) {
          range.push(i)
        }
        range.push(-1) // Ellipsis marker
        range.push(total)
      }
    }

    return range
  })

  // Validation helper
  const isValidPage = (page: number): boolean => {
    return page >= 1 && page <= totalPages.value && Number.isInteger(page)
  }

  // Methods
  const setPage = (page: number): void => {
    if (!isValidPage(page)) {
      console.warn(`Invalid page number: ${page}. Must be between 1 and ${totalPages.value}`)
      return
    }

    const oldPage = paginationState.currentPage
    paginationState.currentPage = page

    if (onPageChange && oldPage !== page) {
      onPageChange(page, paginationState.pageSize)
    }
  }

  const setPageSize = (size: number): void => {
    if (size <= 0 || !Number.isInteger(size)) {
      console.warn(`Invalid page size: ${size}. Must be a positive integer`)
      return
    }

    const oldSize = paginationState.pageSize
    const oldPage = paginationState.currentPage

    paginationState.pageSize = size

    // Adjust current page to maintain roughly the same position
    const currentStartIndex = (oldPage - 1) * oldSize
    const newPage = Math.floor(currentStartIndex / size) + 1
    const validNewPage = Math.min(newPage, Math.ceil(paginationState.total / size) || 1)

    paginationState.currentPage = validNewPage

    if (onPageSizeChange && (oldSize !== size || oldPage !== validNewPage)) {
      onPageSizeChange(validNewPage, size)
    }
  }

  const setTotal = (newTotal: number): void => {
    if (newTotal < 0 || !Number.isInteger(newTotal)) {
      console.warn(`Invalid total: ${newTotal}. Must be a non-negative integer`)
      return
    }

    paginationState.total = newTotal

    // Adjust current page if it's now out of bounds
    const maxPage = Math.ceil(newTotal / paginationState.pageSize) || 1
    if (paginationState.currentPage > maxPage) {
      setPage(maxPage)
    }
  }

  const nextPage = (): void => {
    if (hasNextPage.value) {
      setPage(paginationState.currentPage + 1)
    }
  }

  const prevPage = (): void => {
    if (hasPrevPage.value) {
      setPage(paginationState.currentPage - 1)
    }
  }

  const firstPage = (): void => {
    setPage(1)
  }

  const lastPage = (): void => {
    setPage(totalPages.value)
  }

  const jumpToPage = (page: number): void => {
    setPage(page)
  }

  const reset = (): void => {
    paginationState.currentPage = defaultCurrentPage
    paginationState.pageSize = defaultPageSize
    paginationState.total = initialTotal
  }

  const updatePagination = (config: Partial<PaginationConfig>): void => {
    if (config.currentPage !== undefined) {
      setPage(config.currentPage)
    }
    if (config.pageSize !== undefined) {
      setPageSize(config.pageSize)
    }
    if (config.total !== undefined) {
      setTotal(config.total)
    }
  }

  // Utility methods
  const getPageData = <T>(data: T[]): T[] => {
    const start = startIndex.value
    const end = start + paginationState.pageSize
    return data.slice(start, end)
  }

  const getPageInfo = () => ({
    current: paginationState.currentPage,
    pageSize: paginationState.pageSize,
    total: paginationState.total,
    totalPages: totalPages.value,
    startIndex: startIndex.value,
    endIndex: endIndex.value
  })

  // Watch for external changes to ensure consistency
  watch(
    () => paginationState.total,
    newTotal => {
      const maxPage = Math.ceil(newTotal / paginationState.pageSize) || 1
      if (paginationState.currentPage > maxPage) {
        setPage(maxPage)
      }
    }
  )

  return {
    // State
    paginationState,
    currentPage: readonly(currentPage),
    pageSize: readonly(pageSize),
    total: readonly(total),

    // Computed
    totalPages: readonly(totalPages),
    hasNextPage: readonly(hasNextPage),
    hasPrevPage: readonly(hasPrevPage),
    startIndex: readonly(startIndex),
    endIndex: readonly(endIndex),
    pageRange: readonly(pageRange),

    // Methods
    setPage,
    setPageSize,
    setTotal,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    jumpToPage,
    reset,
    updatePagination,

    // Utilities
    getPageData,
    isValidPage,
    getPageInfo
  }
}
