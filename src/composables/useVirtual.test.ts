// Unit tests for useVirtual composable
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { ref, nextTick, effectScope } from 'vue'
import { useVirtual } from './useVirtual'
import type { TableRow, VirtualConfig } from '@/types'

// Mock Vue lifecycle hooks
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    onMounted: (fn: Function) => {
      // Execute immediately in tests
      fn()
    },
    onUnmounted: (fn: Function) => {
      // Store cleanup function for manual cleanup in tests
      return fn
    }
  }
})

// Mock DOM elements
const createMockContainer = () => ({
  clientHeight: 400,
  scrollTop: 0,
  scrollTo: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
})

// Test data
const generateTestData = (count: number): TableRow[] => {
  return Array.from({ length: count }, (_, i) => ({
    _id: i + 1,
    name: `Item ${i + 1}`,
    value: Math.random() * 100
  }))
}

describe('useVirtual', () => {
  let scope: any
  const containerRef = ref<HTMLElement | null>(null)
  const data = ref<TableRow[]>([])
  const config = ref<VirtualConfig | undefined>()

  beforeEach(() => {
    scope = effectScope()
    containerRef.value = createMockContainer() as any
    data.value = generateTestData(200) // Large dataset
    config.value = {
      enabled: true,
      threshold: 100,
      itemHeight: 48,
      bufferSize: 10,
      overscan: 5
    }
  })

  afterEach(() => {
    if (scope) {
      scope.stop()
    }
  })

  describe('initialization', () => {
    it('should initialize with correct default state', () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.virtualState.value.scrollTop).toBe(0)
      expect(virtual.virtualState.value.visibleStart).toBe(0)
      expect(virtual.virtualState.value.visibleEnd).toBeGreaterThan(0)
    })

    it('should enable virtual scrolling when data exceeds threshold', () => {
      data.value = generateTestData(150) // Above threshold of 100
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.isVirtualEnabled.value).toBe(true)
    })

    it('should disable virtual scrolling when data is below threshold', () => {
      data.value = generateTestData(50) // Below threshold of 100
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.isVirtualEnabled.value).toBe(false)
    })

    it('should disable virtual scrolling when config disabled', () => {
      config.value = { ...config.value!, enabled: false }
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.isVirtualEnabled.value).toBe(false)
    })
  })

  describe('visible data calculation', () => {
    it('should return all data when virtual scrolling disabled', () => {
      data.value = generateTestData(50)
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.visibleData.value).toEqual(data.value)
    })

    it('should return sliced data when virtual scrolling enabled', () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.visibleData.value.length).toBeLessThan(data.value.length)
      expect(virtual.visibleData.value.length).toBeGreaterThan(0)
    })

    it('should update visible range when scrolling', async () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      const initialStart = virtual.virtualState.value.visibleStart

      // Simulate scroll
      containerRef.value!.scrollTop = 500
      virtual.updateVisibleRange()
      await nextTick()

      expect(virtual.virtualState.value.visibleStart).toBeGreaterThan(initialStart)
    })
  })

  describe('styling', () => {
    it('should provide container styles when virtual scrolling enabled', () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      const styles = virtual.containerStyle.value
      expect(styles.height).toBe('100%')
      expect(styles.overflow).toBe('auto')
    })

    it('should provide scroll area styles with correct height', () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      const styles = virtual.scrollAreaStyle.value
      expect(styles.height).toContain('px')
      expect(parseInt(styles.height)).toBeGreaterThan(0)
    })

    it('should return empty styles when virtual scrolling disabled', () => {
      data.value = generateTestData(50)
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.containerStyle.value).toEqual({})
      expect(virtual.scrollAreaStyle.value).toEqual({})
    })
  })

  describe('scroll methods', () => {
    it('should scroll to specific index', () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      const scrollToSpy = vi.spyOn(containerRef.value!, 'scrollTop', 'set')
      virtual.scrollToIndex(10)

      expect(scrollToSpy).toHaveBeenCalledWith(480) // 10 * 48px
    })

    it('should scroll to top', () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      const scrollToSpy = vi.spyOn(containerRef.value!, 'scrollTop', 'set')
      virtual.scrollToTop()

      expect(scrollToSpy).toHaveBeenCalledWith(0)
    })
  })

  describe('item height calculation', () => {
    it('should return fixed height when configured', () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.getItemHeight(0)).toBe(48)
      expect(virtual.getItemHeight(10)).toBe(48)
    })

    it('should handle auto height configuration', () => {
      config.value = { ...config.value!, itemHeight: 'auto' }
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      // Should fallback to default height
      expect(virtual.getItemHeight(0)).toBe(48)
    })
  })

  describe('performance optimization', () => {
    it('should not render all items for large datasets', () => {
      data.value = generateTestData(10000)
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.visibleData.value.length).toBeLessThan(100) // Much less than 10000
    })

    it('should include buffer items for smooth scrolling', () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      const visibleCount = Math.ceil(400 / 48) // container height / item height
      const expectedWithBuffer = visibleCount + (config.value!.bufferSize! * 2) + config.value!.overscan!

      expect(virtual.visibleData.value.length).toBeGreaterThanOrEqual(visibleCount)
      expect(virtual.visibleData.value.length).toBeLessThanOrEqual(expectedWithBuffer + 1)
    })
  })

  describe('reactivity', () => {
    it('should update when data changes', async () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      // Track initial state
      expect(virtual.visibleData.value.length).toBeGreaterThan(0)

      data.value = generateTestData(300)
      await nextTick()

      // Should recalculate visible range
      expect(virtual.virtualState.value.totalHeight).toBeGreaterThan(0)
    })

    it('should update when config changes', async () => {
      const virtual = scope.run(() => useVirtual({ containerRef, data, config }))!

      expect(virtual.isVirtualEnabled.value).toBe(true)

      config.value = { ...config.value!, enabled: false }
      await nextTick()

      expect(virtual.isVirtualEnabled.value).toBe(false)
    })
  })
})