// Composable for table filtering functionality
import { ref, computed, watch, onScopeDispose, type Ref } from 'vue'
import type { TableRow, TableColumn, FilterConfig } from '@/types'
import {
  applyTextSearch,
  applyComplexFilter,
  createFilterCondition,
  createFilterGroup,
  validateFilterCondition,
  validateFilterGroup,
  getFilterableColumns,
  getColumnUniqueValues,
  highlightMatches,
  type FilterOperator,
  type FilterCondition,
  type FilterGroup,
  type SearchConfig,
  type HighlightInfo
} from '@/utils/filtering'

export interface UseFilteringOptions {
  defaultSearch?: SearchConfig
  defaultFilters?: FilterCondition[]
  caseSensitive?: boolean
  enableRegex?: boolean
  enableHighlight?: boolean
  debounceMs?: number
  onFilterChange?: (config: FilterConfig) => void
  onSearchChange?: (searchConfig: SearchConfig) => void
}

export interface UseFilteringReturn {
  // State
  searchConfig: Ref<SearchConfig>
  filterConditions: Ref<FilterCondition[]>
  filterGroups: Ref<FilterGroup[]>
  highlightInfo: Ref<Map<string, HighlightInfo[]>>

  // Computed
  isFiltered: Ref<boolean>
  isSearching: Ref<boolean>
  filteredData: Ref<TableRow[]>
  filterableColumns: Ref<TableColumn[]>
  searchText: Ref<string>

  // Methods
  setSearchText: (text: string) => void
  setSearchConfig: (config: SearchConfig) => void
  addFilterCondition: (condition: FilterCondition) => void
  removeFilterCondition: (index: number) => void
  updateFilterCondition: (index: number, condition: FilterCondition) => void
  addFilterGroup: (group: FilterGroup) => void
  removeFilterGroup: (index: number) => void
  clearAllFilters: () => void
  clearSearch: () => void
  getColumnValues: (columnKey: string, limit?: number) => any[]
  highlightText: (text: string, columnKey: string, rowId: string) => string

  // Advanced filtering
  createSimpleFilter: (column: string, operator: FilterOperator, value: any) => FilterCondition
  createAdvancedFilter: (conditions: FilterCondition[], operator?: 'and' | 'or') => FilterGroup

  // Validation
  validateFilter: (condition: FilterCondition) => boolean
  validateGroup: (group: FilterGroup) => boolean
}

export function useFiltering(
  data: Ref<TableRow[]>,
  columns: Ref<TableColumn[]>,
  options: UseFilteringOptions = {}
): UseFilteringReturn {
  const {
    defaultSearch,
    defaultFilters = [],
    caseSensitive = false,
    enableHighlight = true,
    debounceMs = 300,
    onFilterChange,
    onSearchChange
  } = options

  // Internal state
  const searchConfig = ref<SearchConfig>(
    defaultSearch || {
      text: '',
      caseSensitive,
      regex: false,
      highlight: enableHighlight
    }
  )

  const filterConditions = ref<FilterCondition[]>([...defaultFilters])
  const filterGroups = ref<FilterGroup[]>([])
  const highlightInfo = ref<Map<string, HighlightInfo[]>>(new Map())

  // Debounced search text
  let searchTimeout: NodeJS.Timeout | null = null

  // Computed properties
  const isFiltered = computed(
    () => filterConditions.value.length > 0 || filterGroups.value.length > 0
  )

  const isSearching = computed(() => searchConfig.value.text.trim().length > 0)

  const filterableColumns = computed(() => getFilterableColumns(columns.value))

  const searchText = computed({
    get: () => searchConfig.value.text,
    set: (value: string) => {
      searchConfig.value.text = value
    }
  })

  const filteredData = computed(() => {
    if (!data.value || data.value.length === 0) {
      return []
    }

    try {
      let result = data.value

      // Apply complex filters first
      if (isFiltered.value) {
        // Combine all filter conditions into groups
        const allConditions = [...filterConditions.value]

        if (allConditions.length > 0) {
          const mainGroup = createFilterGroup(allConditions, 'and')
          result = applyComplexFilter(result, mainGroup)
        }

        // Apply additional filter groups
        for (const group of filterGroups.value) {
          result = applyComplexFilter(result, group)
        }
      }

      // Apply text search
      if (isSearching.value) {
        const searchResult = applyTextSearch(result, searchConfig.value, columns.value)
        highlightInfo.value = searchResult.highlightInfo || new Map()
        result = searchResult.data
      } else {
        highlightInfo.value.clear()
      }

      return result
    } catch (error) {
      console.error('Error filtering data:', error)
      return data.value
    }
  })

  // Methods
  const setSearchText = (text: string): void => {
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }

    searchTimeout = setTimeout(() => {
      searchConfig.value.text = text
      onSearchChange?.(searchConfig.value)
    }, debounceMs)
  }

  const setSearchConfig = (config: SearchConfig): void => {
    searchConfig.value = { ...config }
    onSearchChange?.(searchConfig.value)
  }

  const addFilterCondition = (condition: FilterCondition): void => {
    if (validateFilterCondition(condition, columns.value)) {
      filterConditions.value.push(condition)
      triggerFilterChange()
    } else {
      console.warn('Invalid filter condition:', condition)
    }
  }

  const removeFilterCondition = (index: number): void => {
    if (index >= 0 && index < filterConditions.value.length) {
      filterConditions.value.splice(index, 1)
      triggerFilterChange()
    }
  }

  const updateFilterCondition = (index: number, condition: FilterCondition): void => {
    if (index >= 0 && index < filterConditions.value.length) {
      if (validateFilterCondition(condition, columns.value)) {
        filterConditions.value[index] = condition
        triggerFilterChange()
      } else {
        console.warn('Invalid filter condition:', condition)
      }
    }
  }

  const addFilterGroup = (group: FilterGroup): void => {
    if (validateFilterGroup(group, columns.value)) {
      filterGroups.value.push(group)
      triggerFilterChange()
    } else {
      console.warn('Invalid filter group:', group)
    }
  }

  const removeFilterGroup = (index: number): void => {
    if (index >= 0 && index < filterGroups.value.length) {
      filterGroups.value.splice(index, 1)
      triggerFilterChange()
    }
  }

  const clearAllFilters = (): void => {
    filterConditions.value = []
    filterGroups.value = []
    triggerFilterChange()
  }

  const clearSearch = (): void => {
    searchConfig.value.text = ''
    highlightInfo.value.clear()
    onSearchChange?.(searchConfig.value)
  }

  const getColumnValues = (columnKey: string, limit: number = 100): any[] => {
    return getColumnUniqueValues(data.value, columnKey, limit)
  }

  const highlightText = (text: string, columnKey: string, rowId: string): string => {
    if (!enableHighlight || !highlightInfo.value.has(rowId)) {
      return text
    }

    const rowHighlights = highlightInfo.value.get(rowId)
    if (!rowHighlights) {
      return text
    }

    const columnHighlight = rowHighlights.find(h => h.column === columnKey)
    if (!columnHighlight) {
      return text
    }

    return highlightMatches(text, columnHighlight.matches, 'table-search-highlight')
  }

  // Advanced filtering helpers
  const createSimpleFilter = (
    column: string,
    operator: FilterOperator,
    value: any
  ): FilterCondition => {
    return createFilterCondition(column, operator, value, caseSensitive)
  }

  const createAdvancedFilter = (
    conditions: FilterCondition[],
    operator: 'and' | 'or' = 'and'
  ): FilterGroup => {
    return createFilterGroup(conditions, operator)
  }

  // Validation methods
  const validateFilter = (condition: FilterCondition): boolean => {
    return validateFilterCondition(condition, columns.value)
  }

  const validateGroup = (group: FilterGroup): boolean => {
    return validateFilterGroup(group, columns.value)
  }

  // Helper to trigger filter change callback
  const triggerFilterChange = (): void => {
    const config: FilterConfig = {
      search: searchConfig.value,
      conditions: filterConditions.value,
      groups: filterGroups.value
    }
    onFilterChange?.(config)
  }

  // Watch for column changes and validate filters
  watch(
    columns,
    newColumns => {
      // Validate and filter out invalid conditions
      const validConditions = filterConditions.value.filter(condition =>
        validateFilterCondition(condition, newColumns)
      )

      const validGroups = filterGroups.value.filter(group => validateFilterGroup(group, newColumns))

      if (
        validConditions.length !== filterConditions.value.length ||
        validGroups.length !== filterGroups.value.length
      ) {
        console.warn('Some filter configurations became invalid after column changes')
        filterConditions.value = validConditions
        filterGroups.value = validGroups
        triggerFilterChange()
      }
    },
    { deep: true }
  )

  // Watch for search config changes
  watch(
    searchConfig,
    newConfig => {
      onSearchChange?.(newConfig)
    },
    { deep: true }
  )

  // Cleanup timeout on unmount
  onScopeDispose(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }
  })

  return {
    // State
    searchConfig,
    filterConditions,
    filterGroups,
    highlightInfo,

    // Computed
    isFiltered,
    isSearching,
    filteredData,
    filterableColumns,
    searchText,

    // Methods
    setSearchText,
    setSearchConfig,
    addFilterCondition,
    removeFilterCondition,
    updateFilterCondition,
    addFilterGroup,
    removeFilterGroup,
    clearAllFilters,
    clearSearch,
    getColumnValues,
    highlightText,

    // Advanced filtering
    createSimpleFilter,
    createAdvancedFilter,

    // Validation
    validateFilter,
    validateGroup
  }
}
