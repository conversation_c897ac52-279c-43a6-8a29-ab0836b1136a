import { ref, computed, watch, onMounted } from 'vue'
import type { ThemeName, ThemeVariables, ThemeContext } from '../types/theme'
import { themePresets, defaultThemeVariables } from '../styles/themes'

// Global theme state
const currentTheme = ref<ThemeName | string>('default')
const customVars = ref<Record<string, string>>({})
const transitionsEnabled = ref(true)

// Theme storage key
const THEME_STORAGE_KEY = 'vue-table-theme'
const CUSTOM_VARS_STORAGE_KEY = 'vue-table-custom-vars'

/**
 * Theme composable for managing theme state and operations
 */
export function useTheme(): ThemeContext {
  // Load theme from localStorage on initialization
  const loadStoredTheme = () => {
    try {
      const stored = localStorage.getItem(THEME_STORAGE_KEY)
      if (stored) {
        currentTheme.value = stored
      }

      const storedVars = localStorage.getItem(CUSTOM_VARS_STORAGE_KEY)
      if (storedVars) {
        customVars.value = JSON.parse(storedVars)
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error)
    }
  }

  // Save theme to localStorage
  const saveTheme = () => {
    try {
      localStorage.setItem(THEME_STORAGE_KEY, currentTheme.value)
      localStorage.setItem(CUSTOM_VARS_STORAGE_KEY, JSON.stringify(customVars.value))
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error)
    }
  }

  // Apply theme variables to document root
  const applyThemeVariables = (variables: Partial<ThemeVariables>) => {
    const root = document.documentElement

    // Apply each variable
    Object.entries(variables).forEach(([key, value]) => {
      if (value !== undefined) {
        root.style.setProperty(key, value)
      }
    })
  }

  // Get theme variables for current theme
  const getThemeVariables = computed(() => {
    const preset = themePresets.find(p => p.name === currentTheme.value)
    const baseVariables = preset?.variables || defaultThemeVariables

    // Merge with custom variables
    return {
      ...baseVariables,
      ...customVars.value
    }
  })

  // Set theme
  const setTheme = (theme: ThemeName | string) => {
    const previousTheme = currentTheme.value
    currentTheme.value = theme

    // Apply theme to document
    const root = document.documentElement

    // Remove previous theme data attribute
    if (previousTheme !== theme) {
      root.removeAttribute('data-theme')
      root.setAttribute('data-theme', theme)
    }

    // Apply theme variables
    applyThemeVariables(getThemeVariables.value)

    // Save to localStorage
    saveTheme()

    // Emit theme change event only if theme actually changed
    if (previousTheme !== theme) {
      const event = new CustomEvent('theme-change', {
        detail: { theme, previousTheme }
      })
      document.dispatchEvent(event)
    }
  }

  // Set custom variables
  const setCustomVars = (vars: Record<string, string>) => {
    customVars.value = { ...customVars.value, ...vars }
    applyThemeVariables(getThemeVariables.value)
    saveTheme()
  }

  // Clear custom variables
  const clearCustomVars = () => {
    customVars.value = {}
    applyThemeVariables(getThemeVariables.value)
    saveTheme()
  }

  // Get specific theme variable value
  const getThemeVar = (varName: keyof ThemeVariables): string => {
    const variables = getThemeVariables.value
    return variables[varName] || ''
  }

  // Toggle between light and dark theme
  const toggleDarkMode = () => {
    const isDark = currentTheme.value === 'dark'
    setTheme(isDark ? 'default' : 'dark')
  }

  // Check if current theme is dark
  const isDarkTheme = computed(() => {
    return currentTheme.value === 'dark'
  })

  // Enable/disable transitions
  const setTransitions = (enabled: boolean) => {
    transitionsEnabled.value = enabled
    const root = document.documentElement

    if (enabled) {
      root.style.removeProperty('--table-transition-duration')
    } else {
      root.style.setProperty('--table-transition-duration', '0ms')
    }
  }

  // Watch for theme changes and apply them
  watch(
    () => getThemeVariables.value,
    (newVariables) => {
      applyThemeVariables(newVariables)
    },
    { deep: true }
  )

  // Initialize theme on mount
  onMounted(() => {
    loadStoredTheme()
    setTheme(currentTheme.value)
  })

  return {
    currentTheme: computed(() => currentTheme.value),
    customVars: computed(() => customVars.value),
    transitionsEnabled: computed(() => transitionsEnabled.value),
    isDarkTheme,
    setTheme,
    setCustomVars,
    clearCustomVars,
    getThemeVar,
    toggleDarkMode,
    setTransitions
  }
}

// Global theme instance for sharing across components
let globalThemeInstance: ThemeContext | null = null

/**
 * Get or create global theme instance
 */
export function useGlobalTheme(): ThemeContext {
  if (!globalThemeInstance) {
    globalThemeInstance = useTheme()
  }
  return globalThemeInstance
}