// Composable for table sorting functionality
import { ref, computed, watch, type Ref } from 'vue'
import type { TableRow, TableColumn, SortConfig } from '@/types'
import {
  sortByColumn,
  sortByMultipleColumns,
  validateSortConfig,
  validateMultipleSortConfigs,
  getNextSortDirection,
  updateSortConfigs,
  getSortIndicatorState,
  type CustomSortFunction
} from '@/utils/sorting'

export interface UseSortingOptions {
  multiSort?: boolean
  defaultSortConfigs?: SortConfig[]
  customSortFunctions?: Record<string, CustomSortFunction>
  onSortChange?: (sortConfigs: SortConfig[]) => void
}

export interface UseSortingReturn {
  // State
  sortConfigs: Ref<SortConfig[]>

  // Computed
  isSorted: Ref<boolean>
  sortedData: Ref<TableRow[]>

  // Methods
  handleSort: (column: TableColumn, direction?: 'asc' | 'desc' | null, multiSort?: boolean) => void
  handleHeaderClick: (column: TableColumn, event?: MouseEvent) => void
  setSortConfigs: (configs: SortConfig[]) => void
  clearSort: (columnKey?: string) => void
  getSortState: (column: TableColumn) => ReturnType<typeof getSortIndicatorState>

  // Validation
  validateSort: (config: SortConfig) => boolean
}

export function useSorting(
  data: Ref<TableRow[]>,
  columns: Ref<TableColumn[]>,
  options: UseSortingOptions = {}
): UseSortingReturn {
  const {
    multiSort = false,
    defaultSortConfigs = [],
    customSortFunctions = {},
    onSortChange
  } = options

  // Internal state
  const sortConfigs = ref<SortConfig[]>([...defaultSortConfigs])

  // Computed properties
  const isSorted = computed(() => sortConfigs.value.length > 0)

  const sortedData = computed(() => {
    if (!isSorted.value || !data.value || data.value.length === 0) {
      return data.value
    }

    try {
      if (sortConfigs.value.length === 1) {
        // Single column sorting
        const config = sortConfigs.value[0]
        if (!config) return data.value

        const column = columns.value.find(col => col.key === config.column)

        if (!column) {
          console.warn(`Sort column "${config.column}" not found`)
          return data.value
        }

        const customSortFn = customSortFunctions[column.key]
        return sortByColumn(data.value, column, config.direction, customSortFn)
      } else {
        // Multi-column sorting
        return sortByMultipleColumns(
          data.value,
          sortConfigs.value,
          columns.value,
          customSortFunctions
        )
      }
    } catch (error) {
      console.error('Error sorting data:', error)
      return data.value
    }
  })

  // Methods
  const handleSort = (
    column: TableColumn,
    direction?: 'asc' | 'desc' | null,
    enableMultiSort?: boolean
  ): void => {
    if (!column.sortable) {
      return
    }

    const useMultiSort = enableMultiSort ?? multiSort
    const currentState = getSortIndicatorState(column, sortConfigs.value)

    // Determine new direction
    const newDirection = direction !== undefined
      ? direction
      : getNextSortDirection(currentState.direction)

    // Create new sort config
    const newConfig: SortConfig = {
      column: column.key,
      direction: newDirection || 'asc', // Fallback to 'asc' if null
      priority: currentState.priority ?? 0
    }

    // Update sort configurations
    if (newDirection === null) {
      // Remove sort for this column
      sortConfigs.value = sortConfigs.value.filter(config => config.column !== column.key)
    } else {
      sortConfigs.value = updateSortConfigs(sortConfigs.value, newConfig, useMultiSort)
    }

    // Trigger callback
    onSortChange?.(sortConfigs.value)
  }

  const handleHeaderClick = (column: TableColumn, event?: MouseEvent): void => {
    if (!column.sortable) {
      return
    }

    // Check if multi-sort is enabled by holding Ctrl/Cmd key
    const enableMultiSort = multiSort || (event && (event.ctrlKey || event.metaKey))

    handleSort(column, undefined, enableMultiSort)
  }

  const setSortConfigs = (configs: SortConfig[]): void => {
    // Validate all configs
    const validConfigs = configs.filter(config =>
      validateSortConfig(config, columns.value)
    )

    if (validConfigs.length !== configs.length) {
      console.warn('Some sort configurations were invalid and filtered out')
    }

    sortConfigs.value = validConfigs
    onSortChange?.(sortConfigs.value)
  }

  const clearSort = (columnKey?: string): void => {
    if (columnKey) {
      // Clear sort for specific column
      sortConfigs.value = sortConfigs.value.filter(config => config.column !== columnKey)
    } else {
      // Clear all sorts
      sortConfigs.value = []
    }

    onSortChange?.(sortConfigs.value)
  }

  const getSortState = (column: TableColumn) => {
    return getSortIndicatorState(column, sortConfigs.value)
  }

  const validateSort = (config: SortConfig): boolean => {
    return validateSortConfig(config, columns.value)
  }

  // Watch for column changes and validate sort configs
  watch(
    columns,
    (newColumns) => {
      const validConfigs = sortConfigs.value.filter(config =>
        validateSortConfig(config, newColumns)
      )

      if (validConfigs.length !== sortConfigs.value.length) {
        console.warn('Some sort configurations became invalid after column changes')
        sortConfigs.value = validConfigs
        onSortChange?.(sortConfigs.value)
      }
    },
    { deep: true }
  )

  // Watch for sort config changes and validate
  watch(
    sortConfigs,
    (newConfigs) => {
      if (!validateMultipleSortConfigs(newConfigs, columns.value)) {
        console.warn('Invalid sort configurations detected')
      }
    },
    { deep: true }
  )

  return {
    // State
    sortConfigs,

    // Computed
    isSorted,
    sortedData,

    // Methods
    handleSort,
    handleHeaderClick,
    setSortConfigs,
    clearSort,
    getSortState,

    // Validation
    validateSort
  }
}