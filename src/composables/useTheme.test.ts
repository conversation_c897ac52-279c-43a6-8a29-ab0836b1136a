import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { nextTick } from 'vue'
import { useTheme } from './useTheme'
import { themePresets } from '../styles/themes'

// Mock Vue's onMounted
vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    onMounted: (fn: Function) => {
      // Execute immediately in tests
      fn()
    }
  }
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock document
const documentMock = {
  documentElement: {
    style: {
      setProperty: vi.fn(),
      removeProperty: vi.fn()
    },
    setAttribute: vi.fn(),
    removeAttribute: vi.fn()
  },
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn()
}

Object.defineProperty(global, 'document', {
  value: documentMock,
  writable: true
})

describe('useTheme', () => {
  beforeEach(async () => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)

    // Reset global theme state completely
    vi.resetModules()

    // Reset document mock calls
    documentMock.documentElement.style.setProperty.mockClear()
    documentMock.documentElement.style.removeProperty.mockClear()
    documentMock.documentElement.setAttribute.mockClear()
    documentMock.documentElement.removeAttribute.mockClear()
    documentMock.dispatchEvent.mockClear()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('initialization', () => {
    it('should initialize with default theme', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()
      expect(theme.currentTheme.value).toBe('default')
    })

    it('should load theme from localStorage', async () => {
      localStorageMock.getItem.mockReturnValueOnce('dark')
      const { useTheme } = await import('./useTheme')
      useTheme()
      expect(localStorageMock.getItem).toHaveBeenCalledWith('vue-table-theme')
    })

    it('should load custom variables from localStorage', async () => {
      const customVars = { '--custom-color': '#ff0000' }
      localStorageMock.getItem
        .mockReturnValueOnce('default')
        .mockReturnValueOnce(JSON.stringify(customVars))

      const { useTheme } = await import('./useTheme')
      useTheme()
      expect(localStorageMock.getItem).toHaveBeenCalledWith('vue-table-custom-vars')
    })

    it('should handle localStorage errors gracefully', async () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      expect(theme.currentTheme.value).toBe('default')
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to load theme from localStorage:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe('setTheme', () => {
    it('should set theme and update document', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTheme('dark')
      await nextTick()

      expect(theme.currentTheme.value).toBe('dark')
      expect(documentMock.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'dark')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('vue-table-theme', 'dark')
    })

    it('should apply theme variables to document', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTheme('dark')
      await nextTick()

      expect(documentMock.documentElement.style.setProperty).toHaveBeenCalled()
    })

    it('should dispatch theme change event', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      // Clear any calls from initialization
      vi.clearAllMocks()
      documentMock.dispatchEvent.mockClear()

      theme.setTheme('dark')
      await nextTick()

      expect(documentMock.dispatchEvent).toHaveBeenCalledTimes(1)
      const call = documentMock.dispatchEvent.mock.calls[0][0] as CustomEvent
      expect(call.type).toBe('theme-change')
      expect(call.detail).toEqual({ theme: 'dark', previousTheme: 'default' })
    })

    it('should not dispatch event if theme is the same', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      // Clear any calls from initialization
      vi.clearAllMocks()
      documentMock.dispatchEvent.mockClear()

      // Theme is already 'default', setting it again should not dispatch event
      theme.setTheme('default')
      await nextTick()

      // Should not dispatch event since it's already the default theme
      expect(documentMock.dispatchEvent).not.toHaveBeenCalled()
    })
  })

  describe('setCustomVars', () => {
    it('should set custom variables', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()
      const customVars = { '--custom-color': '#ff0000' }

      theme.setCustomVars(customVars)
      await nextTick()

      expect(theme.customVars.value).toEqual(customVars)
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'vue-table-custom-vars',
        JSON.stringify(customVars)
      )
    })

    it('should merge with existing custom variables', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setCustomVars({ '--color1': '#ff0000' })
      theme.setCustomVars({ '--color2': '#00ff00' })
      await nextTick()

      expect(theme.customVars.value).toEqual({
        '--color1': '#ff0000',
        '--color2': '#00ff00'
      })
    })

    it('should apply custom variables to document', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()
      const customVars = { '--custom-color': '#ff0000' }

      theme.setCustomVars(customVars)
      await nextTick()

      expect(documentMock.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--custom-color',
        '#ff0000'
      )
    })
  })

  describe('toggleDarkMode', () => {
    it('should toggle from default to dark', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.toggleDarkMode()
      await nextTick()

      expect(theme.currentTheme.value).toBe('dark')
      expect(theme.isDarkTheme.value).toBe(true)
    })

    it('should toggle from dark to default', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTheme('dark')
      theme.toggleDarkMode()
      await nextTick()

      expect(theme.currentTheme.value).toBe('default')
      expect(theme.isDarkTheme.value).toBe(false)
    })

    it('should toggle from enterprise to dark', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTheme('enterprise')
      theme.toggleDarkMode()
      await nextTick()

      expect(theme.currentTheme.value).toBe('dark')
      expect(theme.isDarkTheme.value).toBe(true)
    })
  })

  describe('getThemeVar', () => {
    it('should return theme variable value', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()
      const value = theme.getThemeVar('--table-primary')
      expect(typeof value).toBe('string')
    })

    it('should return empty string for non-existent variable', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()
      const value = theme.getThemeVar('--non-existent' as any)
      expect(value).toBe('')
    })
  })

  describe('setTransitions', () => {
    it('should enable transitions', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTransitions(true)
      await nextTick()

      expect(theme.transitionsEnabled.value).toBe(true)
      expect(documentMock.documentElement.style.removeProperty).toHaveBeenCalledWith(
        '--table-transition-duration'
      )
    })

    it('should disable transitions', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTransitions(false)
      await nextTick()

      expect(theme.transitionsEnabled.value).toBe(false)
      expect(documentMock.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--table-transition-duration',
        '0ms'
      )
    })
  })

  describe('isDarkTheme', () => {
    it('should return true for dark theme', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTheme('dark')
      await nextTick()

      expect(theme.isDarkTheme.value).toBe(true)
    })

    it('should return false for non-dark themes', async () => {
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTheme('default')
      expect(theme.isDarkTheme.value).toBe(false)

      theme.setTheme('enterprise')
      expect(theme.isDarkTheme.value).toBe(false)
    })
  })

  describe('localStorage persistence', () => {
    it('should save theme to localStorage', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTheme('dark')
      await nextTick()

      expect(localStorageMock.setItem).toHaveBeenCalledWith('vue-table-theme', 'dark')
    })

    it('should save custom vars to localStorage', async () => {
      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      // Clear all mocks to isolate this test
      vi.clearAllMocks()
      const customVars = { '--custom': '#ff0000' }

      theme.setCustomVars(customVars)
      await nextTick()

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'vue-table-custom-vars',
        JSON.stringify(customVars)
      )
    })

    it('should handle localStorage save errors gracefully', async () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage error')
      })

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Import fresh module instance
      const { useTheme } = await import('./useTheme')
      const theme = useTheme()

      theme.setTheme('dark')
      await nextTick()

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to save theme to localStorage:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe('theme presets', () => {
    it('should have all required theme presets', () => {
      expect(themePresets).toHaveLength(3)
      expect(themePresets.map(p => p.name)).toEqual(['default', 'dark', 'enterprise'])
    })

    it('should have display names for all presets', () => {
      themePresets.forEach(preset => {
        expect(preset.displayName).toBeTruthy()
        expect(typeof preset.displayName).toBe('string')
      })
    })

    it('should have variables for all presets', () => {
      themePresets.forEach(preset => {
        expect(preset.variables).toBeTruthy()
        expect(typeof preset.variables).toBe('object')
      })
    })
  })
})
