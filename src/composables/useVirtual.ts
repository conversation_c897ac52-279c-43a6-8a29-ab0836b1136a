// Virtual scrolling composable for high performance table rendering
import { ref, computed, watch, onMounted, onUnmounted, type Ref } from 'vue'
import type { TableRow, VirtualConfig, VirtualState } from '@/types'

// Default virtual scrolling configuration
const DEFAULT_CONFIG: Required<VirtualConfig> = {
  enabled: true,
  threshold: 100,
  itemHeight: 48,
  bufferSize: 10,
  overscan: 5
}

export interface UseVirtualOptions {
  containerRef: Ref<HTMLElement | null>
  data: Ref<TableRow[]>
  config: Ref<VirtualConfig | undefined>
}

export interface UseVirtualReturn {
  // State
  virtualState: Ref<VirtualState>
  isVirtualEnabled: Ref<boolean>

  // Computed
  visibleData: Ref<TableRow[]>
  containerStyle: Ref<Record<string, string | undefined>>
  scrollAreaStyle: Ref<Record<string, string | undefined>>

  // Methods
  scrollToIndex: (index: number) => void
  scrollToTop: () => void
  updateVisibleRange: () => void
  getItemHeight: (index: number) => number
}

/**
 * Virtual scrolling composable
 * Optimizes rendering performance for large datasets by only rendering visible items
 */
export function useVirtual({ containerRef, data, config }: UseVirtualOptions): UseVirtualReturn {
  // Virtual scrolling state
  const virtualState = ref<VirtualState>({
    scrollTop: 0,
    visibleStart: 0,
    visibleEnd: 0,
    totalHeight: 0,
    containerHeight: 0
  })

  // Check if virtual scrolling is enabled
  const isVirtualEnabled = computed(() => {
    const cfg = config.value
    if (!cfg || !cfg.enabled) return false
    return data.value.length >= (cfg.threshold || DEFAULT_CONFIG.threshold)
  })

  // Get merged configuration with defaults
  const mergedConfig = computed(() => ({
    ...DEFAULT_CONFIG,
    ...config.value
  }))

  // Calculate item height (support both fixed and dynamic heights)
  const getItemHeight = (_index: number): number => {
    const cfg = mergedConfig.value
    if (cfg.itemHeight === 'auto') {
      // For auto height, we need to measure actual heights
      // This is a simplified version - in practice, you'd maintain a cache
      return 48 // fallback height
    }
    return typeof cfg.itemHeight === 'number' ? cfg.itemHeight : 48
  }

  // Calculate total height of all items
  const totalHeight = computed(() => {
    if (!isVirtualEnabled.value) return 0

    const cfg = mergedConfig.value
    if (cfg.itemHeight === 'auto') {
      // Sum up all item heights (simplified)
      return data.value.length * 48
    }

    return data.value.length * (cfg.itemHeight as number)
  })

  // Update virtual state when data or container size changes
  const updateVisibleRange = () => {
    if (!isVirtualEnabled.value || !containerRef.value) return

    const container = containerRef.value
    const containerHeight = container.clientHeight
    const scrollTop = container.scrollTop
    const cfg = mergedConfig.value
    const itemHeight = getItemHeight(0) // Assume uniform height for now

    // Calculate visible range
    const visibleStart = Math.max(0, Math.floor(scrollTop / itemHeight) - cfg.bufferSize)
    const visibleCount = Math.ceil(containerHeight / itemHeight) + cfg.bufferSize * 2 + cfg.overscan
    const visibleEnd = Math.min(data.value.length, visibleStart + visibleCount)

    // Update state
    virtualState.value = {
      scrollTop,
      visibleStart,
      visibleEnd,
      totalHeight: totalHeight.value,
      containerHeight
    }
  }

  // Get visible data slice
  const visibleData = computed(() => {
    if (!isVirtualEnabled.value) {
      return data.value
    }

    const { visibleStart, visibleEnd } = virtualState.value
    return data.value.slice(visibleStart, visibleEnd)
  })

  // Container style for positioning
  const containerStyle = computed(() => {
    if (!isVirtualEnabled.value) return {}

    return {
      height: '100%',
      overflow: 'auto',
      position: 'relative'
    }
  })

  // Scroll area style (creates the scrollable area)
  const scrollAreaStyle = computed(() => {
    if (!isVirtualEnabled.value) return {}

    const { visibleStart } = virtualState.value
    const itemHeight = getItemHeight(0)
    const offsetY = visibleStart * itemHeight

    return {
      height: `${totalHeight.value}px`,
      transform: `translateY(${offsetY}px)`,
      position: 'relative'
    }
  })

  // Scroll to specific index
  const scrollToIndex = (index: number) => {
    if (!containerRef.value || !isVirtualEnabled.value) return

    const itemHeight = getItemHeight(index)
    const scrollTop = index * itemHeight
    containerRef.value.scrollTop = scrollTop
  }

  // Scroll to top
  const scrollToTop = () => {
    if (containerRef.value) {
      containerRef.value.scrollTop = 0
    }
  }

  // Handle scroll events (throttled for performance)
  let scrollTimeout: ReturnType<typeof setTimeout> | undefined
  const handleScroll = () => {
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
    scrollTimeout = setTimeout(updateVisibleRange, 16) // ~60fps
  }

  // Watch for data changes
  watch(
    [data, config],
    () => {
      updateVisibleRange()
    },
    { immediate: true }
  )

  // Setup scroll listener
  onMounted(() => {
    if (containerRef.value && isVirtualEnabled.value) {
      containerRef.value.addEventListener('scroll', handleScroll, { passive: true })
      updateVisibleRange()
    }
  })

  // Cleanup
  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener('scroll', handleScroll)
    }
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
  })

  return {
    virtualState,
    isVirtualEnabled,
    visibleData,
    containerStyle,
    scrollAreaStyle,
    scrollToIndex,
    scrollToTop,
    updateVisibleRange,
    getItemHeight
  }
}
