// Vue Table Component - Main Entry Point
// Import component-specific styles only (assumes Tailwind CSS is already configured in the host project)
import './styles/components.css'

export { default as VueTable } from './components/Table/Table.vue'

// Core components (imported on demand)
export { default as ThemeProvider } from './components/ThemeProvider/ThemeProvider.vue'
export { default as ThemeSwitcher } from './components/ThemeSwitcher/ThemeSwitcher.vue'

// Core composables (most commonly used)
export { useTheme } from './composables/useTheme'
export { useSorting } from './composables/useSorting'
export { useFiltering } from './composables/useFiltering'
export { usePagination } from './composables/usePagination'

// Core types
export type { TableConfig, TableColumn, TableRow } from './types'
export type { ThemeName, ThemeVariables } from './types/theme'

// Theme presets
export { themePresets, defaultThemeVariables } from './styles/themes'
