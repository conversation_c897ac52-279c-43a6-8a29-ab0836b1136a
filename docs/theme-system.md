# Vue Table Component - Theme System

## Overview

The Vue Table Component includes a comprehensive theme system built on CSS variables and Tailwind CSS 4, providing flexible styling options with smooth transitions and multiple preset themes.

## Features

- **3 Preset Themes**: Default Light, Dark, and Enterprise Blue
- **CSS Variables**: Full customization through CSS custom properties
- **Smooth Transitions**: Animated theme switching with configurable transitions
- **TypeScript Support**: Complete type definitions for all theme-related APIs
- **Accessibility**: WCAG-compliant color contrasts and reduced motion support
- **Persistence**: Automatic localStorage persistence of theme preferences

## Quick Start

### Basic Usage

```vue
<template>
  <ThemeProvider
    theme="default"
    :enable-transitions="true"
  >
    <VueTable :config="tableConfig" />
  </ThemeProvider>
</template>

<script setup>
import { ThemeProvider, VueTable } from 'vue-table-component'
</script>
```

### With Theme Switcher

```vue
<template>
  <ThemeProvider>
    <template #default="{ theme, setTheme, isDark }">
      <div class="app">
        <ThemeSwitcher
          :show-dark-toggle="true"
          :show-transitions-toggle="true"
          @theme-change="onThemeChange"
        />
        <VueTable :config="tableConfig" />
      </div>
    </template>
  </ThemeProvider>
</template>

<script setup>
import { ThemeProvider, ThemeSwitcher, VueTable } from 'vue-table-component'

const onThemeChange = theme => {
  console.log('Theme changed to:', theme)
}
</script>
```

## Components

### ThemeProvider

The main theme provider component that manages theme state and applies CSS variables.

#### Props

- `theme?: string` - Initial theme name (default: 'default')
- `config?: ThemeConfig` - Theme configuration object
- `enableTransitions?: boolean` - Enable smooth transitions (default: true)

#### Events

- `theme-change(theme: string, previousTheme: string)` - Emitted when theme changes
- `theme-ready()` - Emitted when theme system is initialized

#### Slots

- `default` - Scoped slot with theme utilities

### ThemeSwitcher

A pre-built theme switcher component with controls for theme selection and options.

#### Props

- `label?: string` - Label text (default: 'Theme')
- `showLabel?: boolean` - Show label (default: true)
- `showDarkToggle?: boolean` - Show dark mode toggle (default: true)
- `showTransitionsToggle?: boolean` - Show transitions toggle (default: true)
- `size?: 'sm' | 'md' | 'lg'` - Component size (default: 'md')

#### Events

- `theme-change(theme: string)` - Theme selection changed
- `dark-mode-toggle(isDark: boolean)` - Dark mode toggled
- `transitions-toggle(enabled: boolean)` - Transitions toggled

## Composables

### useTheme()

The main theme composable for managing theme state.

```typescript
import { useTheme } from 'vue-table-component'

const {
  currentTheme, // Current theme name (reactive)
  customVars, // Custom CSS variables (reactive)
  isDarkTheme, // Is current theme dark (computed)
  transitionsEnabled, // Are transitions enabled (reactive)
  setTheme, // Set theme function
  setCustomVars, // Set custom variables function
  toggleDarkMode, // Toggle dark mode function
  getThemeVar, // Get theme variable value function
  setTransitions // Enable/disable transitions function
} = useTheme()
```

## Preset Themes

### Default Light Theme

- Primary: `#3b82f6` (Blue)
- Background: `#ffffff` (White)
- Text: `#1e293b` (Dark Gray)
- Suitable for: General purpose applications

### Dark Theme

- Primary: `#60a5fa` (Light Blue)
- Background: `#0f172a` (Dark Blue)
- Text: `#f1f5f9` (Light Gray)
- Suitable for: Low-light environments, modern applications

### Enterprise Blue Theme

- Primary: `#1e40af` (Deep Blue)
- Background: `#f8fafc` (Light Gray)
- Text: `#0f172a` (Dark)
- Suitable for: Corporate applications, professional interfaces

## CSS Variables

The theme system uses CSS custom properties for all styling values:

### Colors

```css
--table-primary: #3b82f6;
--table-primary-hover: #2563eb;
--table-primary-active: #1d4ed8;
--table-bg: #ffffff;
--table-bg-secondary: #f8fafc;
--table-text: #1e293b;
--table-text-secondary: #64748b;
--table-border: #e2e8f0;
--table-success: #10b981;
--table-warning: #f59e0b;
--table-error: #ef4444;
--table-info: #06b6d4;
```

### Spacing & Layout

```css
--table-padding: 0.75rem;
--table-padding-sm: 0.5rem;
--table-padding-lg: 1rem;
--table-border-radius: 0.375rem;
--table-border-radius-sm: 0.25rem;
```

### Typography

```css
--table-font-size: 0.875rem;
--table-font-size-sm: 0.75rem;
--table-font-size-lg: 1rem;
--table-font-weight: 400;
--table-font-weight-bold: 600;
```

### Effects

```css
--table-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
--table-transition: all 150ms ease-in-out;
--table-transition-fast: all 100ms ease-in-out;
```

## Custom Themes

### Creating Custom Themes

You can create custom themes by providing CSS variable overrides:

```typescript
import { useTheme } from 'vue-table-component'

const { setCustomVars } = useTheme()

// Apply custom colors
setCustomVars({
  '--table-primary': '#ff6b35',
  '--table-bg': '#fef7f0',
  '--table-text': '#2d1b14'
})
```

### CSS-based Custom Themes

Define custom themes in CSS:

```css
[data-theme='custom'] {
  --table-primary: #ff6b35;
  --table-primary-hover: #e55a2b;
  --table-bg: #fef7f0;
  --table-text: #2d1b14;
  --table-border: #f4d5c7;
}
```

Then apply the theme:

```typescript
const { setTheme } = useTheme()
setTheme('custom')
```

## Tailwind CSS Integration

The theme system is fully integrated with Tailwind CSS 4:

```javascript
// tailwind.config.js
export default {
  theme: {
    extend: {
      colors: {
        'table-primary': 'var(--table-primary)',
        'table-bg': 'var(--table-bg)',
        'table-text': 'var(--table-text)'
        // ... other variables
      }
    }
  }
}
```

Use in templates:

```vue
<template>
  <div class="bg-table-bg text-table-text border-table-border">
    <button class="bg-table-primary hover:bg-table-primary-hover">Click me</button>
  </div>
</template>
```

## Accessibility

The theme system includes accessibility features:

- **High Contrast**: All themes meet WCAG AA contrast requirements
- **Reduced Motion**: Respects `prefers-reduced-motion` media query
- **Color Blind Friendly**: Uses patterns and shapes in addition to colors
- **Focus Indicators**: Clear focus states for keyboard navigation

## Performance

- **CSS Variables**: Efficient theme switching without style recalculation
- **Minimal Bundle**: Only includes used theme features
- **Lazy Loading**: Theme presets loaded on demand
- **Memory Efficient**: Automatic cleanup of unused theme data

## Browser Support

- Chrome 49+
- Firefox 31+
- Safari 9.1+
- Edge 16+

## Migration Guide

### From v1.x to v2.x

The theme system has been completely rewritten. Key changes:

1. **CSS Variables**: Now uses CSS custom properties instead of SCSS variables
2. **Component API**: New `ThemeProvider` component required
3. **Composables**: New `useTheme()` composable replaces old theme utilities
4. **Preset Names**: Theme names have changed (see preset documentation)

### Migration Steps

1. Wrap your app with `ThemeProvider`:

```vue
<!-- Before -->
<VueTable theme="dark" />

<!-- After -->
<ThemeProvider theme="dark">
  <VueTable />
</ThemeProvider>
```

2. Update custom theme definitions:

```css
/* Before (SCSS) */
$table-primary: #custom-color;

/* After (CSS) */
:root {
  --table-primary: #custom-color;
}
```

3. Update theme switching code:

```typescript
// Before
import { setTheme } from 'vue-table-component'
setTheme('dark')

// After
import { useTheme } from 'vue-table-component'
const { setTheme } = useTheme()
setTheme('dark')
```

## Examples

See the `/playground` directory for complete examples:

- Basic theme usage
- Custom theme creation
- Theme switcher implementation
- Tailwind CSS integration
- Accessibility features

## API Reference

For complete API documentation, see the TypeScript definitions in `/src/types/theme.ts`.
