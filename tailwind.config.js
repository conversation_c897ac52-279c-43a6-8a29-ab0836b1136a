/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}',
    './playground/**/*.{vue,js,ts,jsx,tsx}'
  ],
  theme: {
    extend: {
      colors: {
        // 默认主题色彩
        'table-primary': 'var(--table-primary)',
        'table-primary-hover': 'var(--table-primary)',
        'table-primary-active': 'var(--table-primary)',
        'table-secondary': 'var(--table-secondary)',
        'table-accent': 'var(--table-accent)',

        // 背景色
        'table-bg': 'var(--table-background)',
        'table-bg-secondary': 'var(--table-surface)',
        'table-header-bg': 'var(--table-surface)',
        'table-row-hover-bg': 'var(--table-hover)',
        'table-row-selected-bg': 'var(--table-selected)',

        // 边框色
        'table-border': 'var(--table-border)',
        'table-border-light': 'var(--table-border)',

        // 文本色
        'table-text': 'var(--table-text)',
        'table-text-secondary': 'var(--table-text-secondary)',
        'table-text-disabled': 'var(--table-text-secondary)',

        // 状态色
        'table-hover': 'var(--table-hover)',
        'table-selected': 'var(--table-selected)',
        'table-focus': 'var(--table-focus)',
        'table-error': 'var(--table-error)',
        'table-warning': 'var(--table-warning)',
        'table-success': 'var(--table-success)',
        'table-info': 'var(--table-info)',

        // 兼容旧的命名
        'table-background': 'var(--table-background)',
        'table-surface': 'var(--table-surface)'
      },
      spacing: {
        'table-xs': 'var(--table-spacing-xs)',
        'table-sm': 'var(--table-spacing-sm)',
        'table-md': 'var(--table-spacing-md)',
        'table-lg': 'var(--table-spacing-lg)',
        'table-xl': 'var(--table-spacing-xl)'
      },
      borderRadius: {
        table: 'var(--table-border-radius)',
        'table-sm': 'var(--table-border-radius-sm)',
        'table-lg': 'var(--table-border-radius-lg)'
      },
      fontSize: {
        'table-xs': 'var(--table-font-size-xs)',
        'table-sm': 'var(--table-font-size-sm)',
        'table-base': 'var(--table-font-size-base)',
        'table-lg': 'var(--table-font-size-lg)',
        'table-xl': 'var(--table-font-size-xl)'
      },
      fontWeight: {
        'table-normal': 'var(--table-font-weight-normal)',
        'table-medium': 'var(--table-font-weight-medium)',
        'table-semibold': 'var(--table-font-weight-semibold)',
        'table-bold': 'var(--table-font-weight-bold)'
      },
      boxShadow: {
        table: 'var(--table-shadow)',
        'table-sm': 'var(--table-shadow-sm)',
        'table-lg': 'var(--table-shadow-lg)',
        'table-focus': 'var(--table-shadow-focus)'
      },
      transitionDuration: {
        table: 'var(--table-transition-duration)'
      },
      zIndex: {
        'table-header': '10',
        'table-fixed': '20',
        'table-dropdown': '30',
        'table-modal': '40',
        'table-tooltip': '50'
      }
    }
  },
  plugins: [],
  // 支持暗色模式
  darkMode: ['class', '[data-theme="dark"]']
}
