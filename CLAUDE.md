# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a high-performance Vue 3 table component library for B2B systems with TypeScript support. The project uses Vue 3, TypeScript, Tailwind CSS 4, and Vite for development and build tooling.

## Development Commands

```bash
# Start development server (runs on port 3200)
npm run dev

# Build for production (demo/development)
npm run build

# Build library for distribution
npm run build:lib

# Run tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests with coverage (90% threshold required)
npm run test:coverage

# Lint and fix code
npm run lint

# Check linting without fixing
npm run lint:check

# Format code
npm run format

# Check formatting
npm run format:check

# TypeScript type checking
npm run type-check
```

## Project Architecture

### Core Components Structure
- **Table.vue**: Main table component with error boundaries, loading states, and keyboard navigation
- **TableHeader.vue**, **TableBody.vue**, **TableRow.vue**, **TableCell.vue**: Modular table sub-components
- **TablePagination.vue**: Pagination component with configurable options
- **ThemeProvider.vue**: Theme context provider for theme system

### Composables Pattern
The project uses Vue 3 composables for reusable logic:
- **useTheme**: Theme management with localStorage persistence
- **useSorting**: Multi-column sorting with custom sort functions
- **useFiltering**: Text filtering with regex support and highlighting
- **usePagination**: Pagination state management

### Type System
Comprehensive TypeScript types are defined in `/src/types/`:
- **table.ts**: Core table configuration types
- **theme.ts**: Theme system types  
- **events.ts**: Event handling types
- **slots.ts**: Slot system types

### Theme System
Built on CSS variables with Tailwind CSS 4:
- Supports 3 preset themes: Default Light, Dark, Enterprise Blue
- CSS custom properties for full customization
- Smooth theme transitions with animation support
- WCAG-compliant accessibility features

## Build System

The project uses Vite with dual build modes:
- **Development mode**: Regular Vue app build for demos and testing
- **Library mode** (`npm run build:lib`): Creates distributable ES and CommonJS modules with TypeScript declarations

### Path Aliases
```typescript
'@': './src'
'@/components': './src/components'  
'@/composables': './src/composables'
'@/types': './src/types'
'@/utils': './src/utils'
'@/styles': './src/styles'
```

## Testing

Uses Vitest with comprehensive coverage requirements:
- **Coverage threshold**: 90% for branches, functions, lines, and statements
- **Test environment**: jsdom for DOM testing
- **Test utilities**: @vue/test-utils for Vue component testing
- All major components and composables have dedicated test files

## Key Features Implementation

- **Virtual scrolling**: For handling large datasets efficiently
- **Keyboard navigation**: Full accessibility support with arrow keys and shortcuts
- **Inline editing**: Cell-level editing capabilities
- **Multi-column sorting**: With custom sort function support
- **Advanced filtering**: Text search with regex and highlighting
- **Responsive design**: Mobile-first approach with configurable breakpoints
- **Data export**: CSV and Excel export capabilities
- **Error boundaries**: Graceful error handling with retry mechanisms

## Code Quality Standards

- TypeScript strict mode enabled
- ESLint + Prettier for code consistency
- Comprehensive unit tests required for all new features
- CSS-in-JS approach avoided in favor of Tailwind CSS utilities
- Component props use comprehensive TypeScript interfaces